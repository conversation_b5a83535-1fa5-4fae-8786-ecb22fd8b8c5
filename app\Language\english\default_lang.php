<?php

/* NOTE: DO NOT CHANGE THIS FILE. IF YOU WANT TO UPDATE THE LANGUAGE THEN COPY THIS FILE TO custom_lang.php AND UPDATE THERE */

/* language locale */
$lang["language_locale"] = "en"; //locale code
$lang["language_locale_long"] = "en-US"; //long locale code
$lang["text_direction"] = "ltr"; //supported value ltr/rtl

/* common */
$lang["add"] = "Add";
$lang["edit"] = "Edit";
$lang["close"] = "Close";
$lang["cancel"] = "Cancel";
$lang["save"] = "Save";
$lang["delete"] = "Delete";
$lang["description"] = "Description";
$lang["admin"] = "Admin";
$lang["manager"] = "Manager";
$lang["options"] = "Options";
$lang["id"] = "ID";
$lang["name"] = "Name";
$lang["email"] = "Email";
$lang["username"] = "Username";
$lang["password"] = "Password";
$lang["retype_password"] = "Retype password";
$lang["previous"] = "Previous";
$lang["next"] = "Next";
$lang["active"] = "Active";
$lang["inactive"] = "Inactive";
$lang["status"] = "Status";
$lang["start_date"] = "Start date";
$lang["end_date"] = "End date";
$lang["start_time"] = "Start time";
$lang["end_time"] = "End time";
$lang["deadline"] = "Deadline";
$lang["added"] = "Added";
$lang["created_date"] = "Created date";
$lang["created"] = "Created";
$lang["created_by"] = "Created by";
$lang["updated"] = "Updated";
$lang["deleted"] = "Deleted";
$lang["currency"] = "Currency";
$lang["new"] = "New";
$lang["open"] = "Open";
$lang["closed"] = "Closed";
$lang["date"] = "Date";
$lang["yes"] = "Yes";
$lang["no"] = "No";
$lang["add_more"] = "Add more";
$lang["crop"] = "Crop";
$lang["income"] = "Income";
$lang["income_vs_expenses"] = "Income vs Expenses";

$lang["title"] = "Title";
$lang["reset"] = "Reset";
$lang["share_with"] = "Share with";
$lang["company_name"] = "Company name";
$lang["address"] = "Address";
$lang["city"] = "City";
$lang["state"] = "State";
$lang["zip"] = "Zip";
$lang["country"] = "Country";
$lang["phone"] = "Phone";
$lang["private"] = "Private";
$lang["website"] = "Website";

$lang["sunday"] = "Sunday";
$lang["monday"] = "Monday";
$lang["tuesday"] = "Tuesday";
$lang["wednesday"] = "Wednesday";
$lang["thursday"] = "Thursday";
$lang["friday"] = "Friday";
$lang["saturday"] = "Saturday";

$lang["daily"] = "Daily";
$lang["monthly"] = "Monthly";
$lang["weekly"] = "Weekly";
$lang["yearly"] = "Yearly";

$lang["see_all"] = "See All";

/* messages */
$lang["error_occurred"] = "Sorry, an error occurred during processing the action! <br /> Please try again later.";
$lang["field_required"] = "This field is required.";
$lang["end_date_must_be_equal_or_greater_than_start_date"] = "End date must be equal or greater than Start date.";
$lang["date_must_be_equal_or_greater_than_today"] = "Date must be equal or greater than today.";
$lang["enter_valid_email"] = "Please enter a valid email address.";
$lang["enter_same_value"] = "Please enter the same value again.";
$lang["record_saved"] = "The record has been saved.";
$lang["record_updated"] = "The record has been updated.";
$lang["record_cannot_be_deleted"] = "The record is in use, you can't delete the record!";
$lang["record_deleted"] = "The record has been deleted.";
$lang["record_undone"] = "The record has been undone.";
$lang["settings_updated"] = "The settings has been updated.";
$lang["enter_minimum_6_characters"] = "Please enter at least 6 characters.";
$lang["message_sent"] = "The message has been sent.";
$lang["invalid_file_type"] = "File type is not allowed.";
$lang["something_went_wrong"] = "Oops, something went wrong!";
$lang["duplicate_email"] = "The email address you have entered is already registered.";
$lang["comment_submited"] = "The comment has been submitted.";
$lang["no_new_messages"] = "You have no new messages";
$lang["sent_you_a_message"] = "Sent you a message";
$lang["max_file_size_3mb_message"] = "File size should not be larger than 3MB";
$lang["keep_it_blank_to_use_default"] = "Keep it blank to use the default";
$lang["admin_user_has_all_power"] = "Admin user's has power to access/modify everything in this system!";
$lang["no_posts_to_show"] = "No posts to show";

/* team_member */
$lang["add_team_member"] = "Add member";
$lang["edit_team_member"] = "Edit team member";
$lang["delete_team_member"] = "Delete team member";
$lang["team_member"] = "Team member";
$lang["team_members"] = "Team members";
$lang["active_members"] = "Active members";
$lang["inactive_members"] = "Inactive members";
$lang["first_name"] = "First name";
$lang["last_name"] = "Last name";
$lang["mailing_address"] = "Mailing address";
$lang["alternative_address"] = "Alternative address";
$lang["phone"] = "Phone";
$lang["alternative_phone"] = "Alternative phone";
$lang["gender"] = "Gender";
$lang["male"] = "Male";
$lang["female"] = "Female";
$lang["date_of_birth"] = "Date of birth";
$lang["date_of_hire"] = "Date of hire";
$lang["ssn"] = "SSN";
$lang["salary"] = "Salary";
$lang["salary_term"] = "Salary term";
$lang["job_info"] = "Job Info";
$lang["job_title"] = "Job Title";
$lang["general_info"] = "General Info";
$lang["account_settings"] = "Account settings";
$lang["list_view"] = "List view";
$lang["profile_image_changed"] = "The profile image has been changed.";
$lang["send_invitation"] = "Send invitation";
$lang["invitation_sent"] = "The invitation has been sent.";
$lang["reset_info_send"] = "If there is any account with the email address, we'll send instructions. Please check your email.";
$lang["profile"] = "Profile";
$lang["my_profile"] = "My Profile";
$lang["change_password"] = "Change Password";
$lang["social_links"] = "Social Links";
$lang["view_details"] = "View Details";
$lang["invite_someone_to_join_as_a_team_member"] = "Invite someone to join as a team member.";

/* team */
$lang["add_team"] = "Add team";
$lang["edit_team"] = "Edit team";
$lang["delete_team"] = "Delete team";
$lang["team"] = "Team";
$lang["select_a_team"] = "Select a team";

/* dashboard */
$lang["dashboard"] = "Dashboard";

/* attendance */
$lang["add_attendance"] = "Add time manually";
$lang["edit_attendance"] = "Edit time card";
$lang["delete_attendance"] = "Delete time card";
$lang["attendance"] = "Time cards";
$lang["clock_in"] = "Clock In";
$lang["clock_out"] = "Clock Out";
$lang["in_date"] = "In Date";
$lang["out_date"] = "Out Date";
$lang["in_time"] = "In Time";
$lang["out_time"] = "Out Time";
$lang["clock_started_at"] = "Clock started at";
$lang["you_are_currently_clocked_out"] = "You are currently clocked out";
$lang["members_clocked_in"] = "Members Clocked In";
$lang["members_clocked_out"] = "Members Clocked Out";
$lang["my_time_cards"] = "My time cards";
$lang["timecard_statistics"] = "Time Card Statistics";
$lang["total_hours_worked"] = "Total hours worked";
$lang["total_project_hours"] = "Total project hours";

/* leave types */
$lang["add_leave_type"] = "Add leave type";
$lang["edit_leave_type"] = "Edit leave type";
$lang["delete_leave_type"] = "Delete leave type";
$lang["leave_type"] = "Leave type";
$lang["leave_types"] = "Leave types";

/* leave */
$lang["apply_leave"] = "Apply leave";
$lang["assign_leave"] = "Assign leave";
$lang["leaves"] = "Leave";
$lang["pending_approval"] = "Pending approval";
$lang["all_applications"] = "All applications";
$lang["duration"] = "Duration";
$lang["single_day"] = "Single day";
$lang["mulitple_days"] = "Multiple days";
$lang["reason"] = "Reason";
$lang["applicant"] = "Applicant";
$lang["approved"] = "Approved";
$lang["approve"] = "Approve";
$lang["rejected"] = "Rejected";
$lang["reject"] = "Reject";
$lang["canceled"] = "Canceled";
$lang["completed"] = "Completed";
$lang["pending"] = "Pending";
$lang["day"] = "Day";
$lang["days"] = "Days";
$lang["hour"] = "Hour";
$lang["hours"] = "Hours";
$lang["application_details"] = "Application details";
$lang["rejected_by"] = "Rejected by";
$lang["approved_by"] = "Approved by";
$lang["start_date_to_end_date_format"] = "%s to %s";
$lang["my_leave"] = "My leave";

/* events */
$lang["add_event"] = "Add event";
$lang["edit_event"] = "Edit event";
$lang["delete_event"] = "Delete event";
$lang["events"] = "Events";
$lang["event_calendar"] = "Event calendar";
$lang["location"] = "Location";
$lang["event_details"] = "Event details";
$lang["event_deleted"] = "The event has been deleted.";
$lang["view_on_calendar"] = "View on calendar";
$lang["no_event_found"] = "No event found!";
$lang["events_today"] = "Events today";

/* announcement */
$lang["add_announcement"] = "Add announcement";
$lang["edit_announcement"] = "Edit announcement";
$lang["delete_announcement"] = "Delete announcement";
$lang["announcement"] = "Announcement";
$lang["announcements"] = "Announcements";
$lang["all_team_members"] = "All team members";
$lang["all_team_clients"] = "All Clients";

/* settings */
$lang["app_settings"] = "App Settings";
$lang["app_title"] = "App Title";
$lang["site_logo"] = "Site Logo";
$lang["invoice_logo"] = "Invoice Logo";
$lang["timezone"] = "Timezone";
$lang["date_format"] = "Date Format";
$lang["time_format"] = "Time Format";
$lang["first_day_of_week"] = "First Day of Week";
$lang["currency_symbol"] = "Currency Symbol";
$lang["general"] = "General";
$lang["general_settings"] = "General Settings";
$lang["item_purchase_code"] = "Item Purchase Code";
$lang["company"] = "Company";
$lang["company_settings"] = "Company Settings";
$lang["email_settings"] = "Email Settings";
$lang["payment_methods"] = "Payment Methods";
$lang["email_sent_from_address"] = "Email sent from address";
$lang["email_sent_from_name"] = "Email sent from name";
$lang["email_use_smtp"] = "Use SMTP";
$lang["email_smtp_host"] = "SMTP Host";
$lang["email_smtp_user"] = "SMTP User";
$lang["email_smtp_password"] = "SMTP Password";
$lang["email_smtp_port"] = "SMTP Port";
$lang["send_test_mail_to"] = "Send a test mail to";
$lang["test_mail_sent"] = "The test mail has been sent!";
$lang["test_mail_send_failed"] = "Failed to send the test email.";
$lang["settings"] = "Settings";
$lang["updates"] = "Updates";
$lang["current_version"] = "Current Version";
$lang["language"] = "Language";
$lang["ip_restriction"] = "IP Restriction";
$lang["varification_failed_message"] = "Sorry, we could not verify your item purchase code.";
$lang["enter_one_ip_per_line"] = "Enter one IP per line. Keep it blank to allow all IPs. *Admin users will not be affected.";
$lang["allow_timecard_access_from_these_ips_only"] = "Allow timecard access from these IPs only.";
$lang["decimal_separator"] = "Decimal Separator";
$lang["client_settings"] = "Client settings";
$lang["disable_client_login_and_signup"] = "Disable client login and signup";
$lang["disable_client_login_help_message"] = "Client contacts will not be able to login/sign up in this system until you revert this setting.";
$lang["who_can_send_or_receive_message_to_or_from_clients"] = "Who can send/receive message to/from clients";

/* account */
$lang["authentication_failed"] = "Authentication failed!";
$lang["signin"] = "Sign in";
$lang["sign_out"] = "Sign Out";
$lang["you_dont_have_an_account"] = "You don't have an account?";
$lang["already_have_an_account"] = "Already have an account?";
$lang["forgot_password"] = "Forgot password?";
$lang["signup"] = "Sign up";
$lang["input_email_to_reset_password"] = "Input your email to reset your password";
$lang["no_acount_found_with_this_email"] = "Sorry, no account found with this email.";
$lang["reset_password"] = "Reset Password";
$lang["password_reset_successfully"] = "Your password has been reset successfully.";
$lang["account_created"] = "Your account has been created successfully!";
$lang["invitation_expaired_message"] = "The invitation has expired or something went wrong";
$lang["account_already_exists_for_your_mail"] = "Account already exists for your email address.";
$lang["create_an_account_as_a_new_client"] = "Create an account as a new client.";
$lang["create_an_account_as_a_team_member"] = "Create an account as a team member.";
$lang["create_an_account_as_a_client_contact"] = "Create an account as a client contact.";

/* messages */
$lang["messages"] = "Messages";
$lang["message"] = "Message";
$lang["compose"] = "Compose";
$lang["send_message"] = "Send message";
$lang["write_a_message"] = "Write a message...";
$lang["reply_to_sender"] = "Reply to sender...";
$lang["subject"] = "Subject";
$lang["send"] = "Send";
$lang["to"] = "To";
$lang["from"] = "From";
$lang["inbox"] = "Inbox";
$lang["sent_items"] = "Sent items";
$lang["me"] = "Me";
$lang["select_a_message"] = "Select a message to view";

/* clients */
$lang["add_client"] = "Add client";
$lang["edit_client"] = "Edit client";
$lang["delete_client"] = "Delete client";
$lang["client"] = "Client";
$lang["clients"] = "Clients";
$lang["client_details"] = "Client details";
$lang["due"] = "Due";

$lang["add_contact"] = "Add contact";
$lang["edit_contact"] = "Edit contact";
$lang["delete_contact"] = "Delete contact";
$lang["contact"] = "Contact";
$lang["contacts"] = "Contacts";
$lang["users"] = "Users";
$lang["primary_contact"] = "Primary contact";
$lang["disable_login"] = "Disable login";
$lang["disable_login_help_message"] = "The user will not be able to login in this system!";
$lang["email_login_details"] = "Email login details to this user";
$lang["generate"] = "Generate";
$lang["show_text"] = "Show text";
$lang["hide_text"] = "Hide text";
$lang["mark_as_inactive"] = "Mark as inactive";
$lang["mark_as_inactive_help_message"] = "The inactive users will not be able to login in this system and not be counted in the active user list!";

$lang["invoice_id"] = "Invoice ID";
$lang["payments"] = "Payments";
$lang["invoice_sent_message"] = "The invoice has been sent!";
$lang["attached"] = "Attached";
$lang["vat_number"] = "VAT Number";
$lang["invite_an_user"] = "Invite an user for %s"; // Invite an user for {company name}
$lang["unit_type"] = "Unit type";

/* projects */
$lang["add_project"] = "Add project";
$lang["edit_project"] = "Edit project";
$lang["delete_project"] = "Delete project";
$lang["project"] = "Project";
$lang["projects"] = "Projects";
$lang["all_projects"] = "All Projects";
$lang["member"] = "Member";
$lang["overview"] = "Overview";
$lang["project_members"] = "Project members";
$lang["add_member"] = "Add member";
$lang["delete_member"] = "Delete member";
$lang["start_timer"] = "Start timer";
$lang["stop_timer"] = "Stop timer";
$lang["project_timeline"] = "Project Timeline";
$lang["open_projects"] = "Open Projects";
$lang["projects_completed"] = "Projects Completed";
$lang["progress"] = "Progress";
$lang["activity"] = "Activity";
$lang["started_at"] = "Started at";
$lang["customer_feedback"] = "Customer feedback";
$lang["project_comment_reply"] = "Project comment reply";
$lang["task_comment_reply"] = "Task comment reply";
$lang["file_comment_reply"] = "File comment reply   ";
$lang["customer_feedback_reply"] = "Customer feedback reply";

/* expense */
$lang["add_category"] = "Add category";
$lang["edit_category"] = "Edit category";
$lang["delete_category"] = "Delete category";
$lang["category"] = "Category";
$lang["categories"] = "Categories";
$lang["expense_categories"] = "Expense Categories";
$lang["add_expense"] = "Add expense";
$lang["edit_expense"] = "Edit expense";
$lang["delete_expense"] = "Delete expense";
$lang["expense"] = "Expense";
$lang["expenses"] = "Expenses";
$lang["date_of_expense"] = "Date of expense";
$lang["finance"] = "Finance";

/* notes */
$lang["add_note"] = "Add note";
$lang["edit_note"] = "Edit note";
$lang["delete_note"] = "Delete note";
$lang["note"] = "Note";
$lang["notes"] = "Notes";
$lang["sticky_note"] = "Sticky Note (Private)";

/* history */
$lang["history"] = "History";

/* timesheet */
$lang["timesheets"] = "Timesheets";
$lang["log_time"] = "Log time";
$lang["edit_timelog"] = "Edit timelog";
$lang["delete_timelog"] = "Delete timelog";
$lang["timesheet_statistics"] = "Timesheet Statistics";

/* milestones */
$lang["add_milestone"] = "Add milestone";
$lang["edit_milestone"] = "Edit milestone";
$lang["delete_milestone"] = "Delete milestone";
$lang["milestone"] = "Milestone";
$lang["milestones"] = "Milestones";

/* files */
$lang["add_files"] = "Add files";
$lang["edit_file"] = "Edit file";
$lang["delete_file"] = "Delete file";
$lang["file"] = "File";
$lang["files"] = "Files";
$lang["file_name"] = "File name";
$lang["size"] = "Size";
$lang["uploaded_by"] = "Uploaded by";
$lang["accepted_file_format"] = "Accepted file format";
$lang["comma_separated"] = "Comma separated";
$lang["project_file"] = "File";
$lang["download"] = "Download";
$lang["download_files"] = "Download %s files"; //Ex. Download 4 files;
$lang["file_preview_is_not_available"] = "File preview is not available.";

/* tasks */
$lang["add_task"] = "Add task";
$lang["edit_task"] = "Edit task";
$lang["delete_task"] = "Delete task";
$lang["task"] = "Task";
$lang["tasks"] = "Tasks";
$lang["my_tasks"] = "My Tasks";
$lang["my_open_tasks"] = "My open tasks";
$lang["assign_to"] = "Assign to";
$lang["assigned_to"] = "Assigned to";
$lang["labels"] = "Labels";
$lang["to_do"] = "To do";
$lang["in_progress"] = "In progress";
$lang["done"] = "Done";
$lang["task_info"] = "Task info";
$lang["points"] = "Points";
$lang["point"] = "Point";
$lang["task_status"] = "Task Status";

/* comments */
$lang["comment"] = "Comment";
$lang["comments"] = "Comments";
$lang["write_a_comment"] = "Write a comment...";
$lang["write_a_reply"] = "Write a reply...";
$lang["post_comment"] = "Post Comment";
$lang["post_reply"] = "Post Reply";
$lang["reply"] = "Reply";
$lang["replies"] = "Replies";
$lang["like"] = "Like";
$lang["unlike"] = "Unlike";
$lang["view"] = "View";
$lang["project_comment"] = "Project Comment";
$lang["task_comment"] = "Task Comment";
$lang["file_comment"] = "File Comment";

/* time format */
$lang["today"] = "Today";
$lang["yesterday"] = "Yesterday";
$lang["tomorrow"] = "Tomorrow";

$lang["today_at"] = "Today at";
$lang["yesterday_at"] = "Yesterday at";

/* tickets */

$lang["add_ticket"] = "Add ticket";
$lang["ticket"] = "Ticket";
$lang["tickets"] = "Tickets";
$lang["ticket_id"] = "Ticket ID";
$lang["client_replied"] = "Client replied";
$lang["change_status"] = "Change status";
$lang["last_activity"] = "Last activity";
$lang["open_tickets"] = "Open tickets";
$lang["ticket_status"] = "Ticket Status";

/* ticket types */

$lang["add_ticket_type"] = "Add ticket type";
$lang["ticket_type"] = "Ticket type";
$lang["ticket_types"] = "Ticket types";
$lang["edit_ticket_type"] = "Edit ticket type";
$lang["delete_ticket_type"] = "Delete ticket type";

/* payment methods */

$lang["add_payment_method"] = "Add payment method";
$lang["payment_method"] = "Payment method";
$lang["payment_methods"] = "Payment methods";
$lang["edit_payment_method"] = "Edit payment method";
$lang["delete_payment_method"] = "Delete payment method";

/* invoices */

$lang["add_invoice"] = "Add invoice";
$lang["edit_invoice"] = "Edit invoice";
$lang["delete_invoice"] = "Delete invoice";
$lang["invoice"] = "Invoice";
$lang["invoices"] = "Invoices";
$lang["bill_date"] = "Bill date";
$lang["due_date"] = "Due date";
$lang["payment_date"] = "Payment date";
$lang["bill_to"] = "Bill To";
$lang["invoice_value"] = "Invoice Value";
$lang["payment_received"] = "Payment Received";
$lang["invoice_payments"] = "Payments";
$lang["draft"] = "Draft";
$lang["fully_paid"] = "Fully paid";
$lang["partially_paid"] = "Partially paid";
$lang["not_paid"] = "Not paid";
$lang["overdue"] = "Overdue";
$lang["invoice_items"] = "Invoice items";
$lang["edit_invoice"] = "Edit invoice";
$lang["delete_invoice"] = "Delete invoice";
$lang["item"] = "Item";
$lang["add_item"] = "Add item";
$lang["create_new_item"] = "Create new item";
$lang["select_or_create_new_item"] = "Select from list or create new item...";
$lang["quantity"] = "Quantity";
$lang["rate"] = "Rate";
$lang["total_of_all_pages"] = "Total of all pages";
$lang["sub_total"] = "Sub Total";
$lang["total"] = "Total";
$lang["last_email_sent"] = "Last email sent";
$lang["item_library"] = "Item library";
$lang["add_payment"] = "Add payment";
$lang["never"] = "Never";
$lang["email_invoice_to_client"] = "Email invoice to client";
$lang["download_pdf"] = "Download PDF";
$lang["print"] = "Print";
$lang["actions"] = "Actions";
$lang["balance_due"] = "Balance Due";
$lang["paid"] = "Paid";
$lang["amount"] = "Amount";
$lang["invoice_payment_list"] = "Invoice payment list";
$lang["invoice_statistics"] = "Invoice Statistics";
$lang["payment"] = "Payment";

/* email templates */
$lang["email_templates"] = "Email templates";
$lang["select_a_template"] = "Select a template to edit";
$lang["avilable_variables"] = "Available variables";
$lang["restore_to_default"] = "Restore to default";
$lang["template_restored"] = "The template has been restored to default.";
$lang["login_info"] = "Login info";
$lang["team_member_invitation"] = "Team member invitation";
$lang["client_contact_invitation"] = "Client contact invitation";
$lang["send_invoice"] = "Send invoice";
$lang["signature"] = "Signature";

/* roles */

$lang["role"] = "Role";
$lang["roles"] = "Roles";
$lang["add_role"] = "Add role";
$lang["edit_role"] = "Edit role";
$lang["delete_role"] = "Delete role";
$lang["use_seetings_from"] = "Use settings from";
$lang["permissions"] = "Permissions";
$lang["yes_all_members"] = "Yes, all members";
$lang["yes_specific_members_or_teams"] = "Yes, specific members or teams";
$lang["yes_specific_ticket_types"] = "Yes, specific ticket types";
$lang["select_a_role"] = "Select a role";
$lang["choose_members_and_or_teams"] = "Choose members and / or teams";
$lang["choose_ticket_types"] = "Choose ticket types";
$lang["excluding_his_her_time_cards"] = "Excluding his/her own time cards";
$lang["excluding_his_her_leaves"] = "Excluding his/her own leaves";
$lang["can_manage_team_members_leave"] = "Can manage team member's leaves?";
$lang["can_manage_team_members_timecards"] = "Can manage team member's time cards?";
$lang["can_access_invoices"] = "Can access invoices?";
$lang["can_access_expenses"] = "Can access expenses?";
$lang["can_access_clients_information"] = "Can access client's information?";
$lang["can_access_tickets"] = "Can access tickets?";
$lang["can_manage_announcements"] = "Can manage announcements?";

/* timeline */
$lang["post_placeholder_text"] = "Share an idea or documents...";
$lang["post"] = "Post";
$lang["timeline"] = "Timeline";
$lang["load_more"] = "Load more";
$lang["upload_file"] = "Upload File";
$lang["upload"] = "Upload";
$lang["new_posts"] = "New posts";

/* taxes */

$lang["add_tax"] = "Add Tax";
$lang["tax"] = "TAX";
$lang["taxes"] = "Taxes";
$lang["edit_tax"] = "Edit tax";
$lang["delete_tax"] = "Delete tax";
$lang["percentage"] = "Percentage (%)";
$lang["second_tax"] = "Second TAX";

/* Version 1.2 */
$lang["available_on_invoice"] = "Available on Invoice";
$lang["available_on_invoice_help_text"] = "The payment method will be appear in client's invoices.";
$lang["minimum_payment_amount"] = "Minimum payment amount";
$lang["minimum_payment_amount_help_text"] = "Clients will not be able to pay the invoice using this payment method, if the invoice value less than this value.";
$lang["pay_invoice"] = "Pay Invoice";
$lang["pay_button_text"] = "Pay button text";
$lang["minimum_payment_validation_message"] = "The payment amount can't be less then: "; //ex. The payment amount can't be less then: USD 100.00;
$lang["invoice_settings"] = "Invoice Settings";
$lang["allow_partial_invoice_payment_from_clients"] = "Allow partial payment from clients";
$lang["invoice_color"] = "Invoice Color";
$lang["invoice_footer"] = "Invoice Footer";
$lang["invoice_preview"] = "Invoice Preview";
$lang["close_preview"] = "Close Preview";
$lang["only_me"] = "Only me";
$lang["specific_members_and_teams"] = "Specific members and teams";
$lang["rows_per_page"] = "Rows per page";
$lang["price"] = "Price";
$lang["security_type"] = "Security Type";

$lang["client_can_view_tasks"] = "Client can view tasks?";
$lang["client_can_create_tasks"] = "Client can create tasks?";
$lang["client_can_edit_tasks"] = "Client can edit tasks?";
$lang["client_can_comment_on_tasks"] = "Client can comment on tasks?";

$lang["set_project_permissions"] = "Set project permissions";
$lang["can_create_projects"] = "Can create projects";
$lang["can_edit_projects"] = "Can edit projects";
$lang["can_delete_projects"] = "Can delete projects";
$lang["can_create_tasks"] = "Can create tasks";
$lang["can_edit_tasks"] = "Can edit tasks";
$lang["can_delete_tasks"] = "Can delete tasks";
$lang["can_comment_on_tasks"] = "Can comment on tasks";
$lang["can_create_milestones"] = "Can create milestones";
$lang["can_edit_milestones"] = "Can edit milestones";
$lang["can_delete_milestones"] = "Can delete milestones";
$lang["can_add_remove_project_members"] = "Can add/remove project members";
$lang["can_delete_files"] = "Can delete files";

/* Version 1.2.2 */
$lang["label"] = "Label";
$lang["send_bcc_to"] = "When sending invoice to client, send BCC to";
$lang["mark_project_as_completed"] = "Mark Project as Completed";
$lang["mark_project_as_canceled"] = "Mark Project as Canceled";
$lang["mark_project_as_open"] = "Mark Project as Open";

/* Version 1.3 */
$lang["notification"] = "Notification";
$lang["notifications"] = "Notifications";
$lang["notification_settings"] = "Notification Settings";
$lang["enable_email"] = "Enable email";
$lang["enable_web"] = "Enable web";
$lang["event"] = "Event";
$lang["notify_to"] = "Notify to";

$lang["project_created"] = "Project created";
$lang["project_deleted"] = "Project deleted";
$lang["project_task_created"] = "Project task created";
$lang["project_task_updated"] = "Project task updated";
$lang["project_task_assigned"] = "Project task assigned";
$lang["project_task_started"] = "Project task started";
$lang["project_task_finished"] = "Project task finished";
$lang["project_task_reopened"] = "Project task reopened";
$lang["project_task_deleted"] = "Project task deleted";
$lang["project_task_commented"] = "Project task commented";
$lang["project_member_added"] = "Project member added";
$lang["project_member_deleted"] = "Project member deleted";
$lang["project_file_added"] = "Project file added";
$lang["project_file_deleted"] = "Project file deleted";
$lang["project_file_commented"] = "Project file commented";
$lang["project_comment_added"] = "Project comment added";
$lang["project_comment_replied"] = "Project comment replied";
$lang["project_customer_feedback_added"] = "Project customer feedback added";
$lang["project_customer_feedback_replied"] = "Project customer feedback replied";
$lang["client_signup"] = "Client signup";
$lang["invoice_online_payment_received"] = "Invoice online payment received";
$lang["leave_application_submitted"] = "Leave application submitted";
$lang["leave_approved"] = "Leave approved";
$lang["leave_assigned"] = "Leave assigned";
$lang["leave_rejected"] = "Leave rejected";
$lang["leave_canceled"] = "Leave canceled";
$lang["ticket_created"] = "Ticket created";
$lang["ticket_commented"] = "Ticket commented";
$lang["ticket_closed"] = "Ticket closed";
$lang["ticket_reopened"] = "Ticket reopened";
$lang["leave"] = "Leave";

$lang["client_primary_contact"] = "Primary contact of client";
$lang["client_all_contacts"] = "All contacts of client";
$lang["task_assignee"] = "Task assignee";
$lang["task_collaborators"] = "Task collaborators";
$lang["comment_creator"] = "Comment creator";
$lang["leave_applicant"] = "Leave applicant";
$lang["ticket_creator"] = "Ticket creator";

$lang["no_new_notifications"] = "No notification found.";

/* Notification messages */

$lang["notification_project_created"] = "Created a new project.";
$lang["notification_project_deleted"] = "Deleted a project.";
$lang["notification_project_task_created"] = "Created a new task.";
$lang["notification_project_task_updated"] = "Updated a task.";
$lang["notification_project_task_assigned"] = "Assigned a task to %s"; //Assigned a task to Mr. X;
$lang["notification_project_task_started"] = "Started a task.";
$lang["notification_project_task_finished"] = "Finished a task.";
$lang["notification_project_task_reopened"] = "Reopened a task.";
$lang["notification_project_task_deleted"] = "Deleted a task.";
$lang["notification_project_task_commented"] = "Commented on a task.";
$lang["notification_project_member_added"] = "Added %s in a project."; //Added Mr. X in a project.;
$lang["notification_project_member_deleted"] = "Deleted %s from a project."; //Deleted Mr. X from a project.;
$lang["notification_project_file_added"] = "Added a file in project.";
$lang["notification_project_file_deleted"] = "Deleted a file from project.";
$lang["notification_project_file_commented"] = "Commented on a file.";
$lang["notification_project_comment_added"] = "Commented on a project.";
$lang["notification_project_comment_replied"] = "Replied on a project comment.";
$lang["notification_project_customer_feedback_added"] = "Commented on a project.";
$lang["notification_project_customer_feedback_replied"] = "Replied on a comment.";
$lang["notification_client_signup"] = "Signed up as a new client."; //Mr. X signed up as a new client.;
$lang["notification_invoice_online_payment_received"] = "Submitted an online payment.";
$lang["notification_leave_application_submitted"] = "Submitted a leave application.";
$lang["notification_leave_approved"] = "Approved a leave of %s."; //Approved a leave of Mr. X;
$lang["notification_leave_assigned"] = "Assigned a leave to %s."; //Assigned a leave to Mr. X;
$lang["notification_leave_rejected"] = "Rejected a leave %s."; //Approve a leave of Mr. X;
$lang["notification_leave_canceled"] = "Canceled a leave appliction.";
$lang["notification_ticket_created"] = "Created a new ticket.";
$lang["notification_ticket_commented"] = "Commented on a ticket.";
$lang["notification_ticket_closed"] = "Closed the ticket.";
$lang["notification_ticket_reopened"] = "Reopened the ticket.";

$lang["general_notification"] = "General notification";

$lang["disable_online_payment"] = "Disable online payment";
$lang["disable_online_payment_description"] = "Hide online payment options in invoice for this client.";

$lang["client_can_view_project_files"] = "Client can view project files?";
$lang["client_can_add_project_files"] = "Client can add project files?";
$lang["client_can_comment_on_files"] = "Client can comment on files?";
$lang["mark_invoice_as_not_paid"] = "Mark as Not paid"; //Change invoice status to Not Paid;

$lang["set_team_members_permission"] = "Set team members permissions";
$lang["can_view_team_members_contact_info"] = "Can view team member's contact info?";
$lang["can_view_team_members_social_links"] = "Can view team member's social links?";

$lang["collaborator"] = "Collaborator";
$lang["collaborators"] = "Collaborators";

/* Version 1.4 */

$lang["modules"] = "Modules";
$lang["manage_modules"] = "Manage Modules";
$lang["module_settings_instructions"] = "Select the modules you want to use.";

$lang["task_point_help_text"] = "Task point considered as a task value. You can set 5 points for very difficult tasks and 1 point for easy tasks."; //meaning of task point;

$lang["mark_as_open"] = "Mark as Open";
$lang["mark_as_closed"] = "Mark as Closed";

$lang["ticket_assignee"] = "Ticket assignee";

$lang["estimate"] = "Estimate";
$lang["estimates"] = "Estimates";
$lang["estimate_request"] = "Estimate Request";
$lang["estimate_requests"] = "Estimate Requests";
$lang["estimate_list"] = "Estimate List";
$lang["estimate_forms"] = "Estimate Forms";
$lang["estimate_request_forms"] = "Estimate Request Forms";

$lang["add_form"] = "Add form";
$lang["edit_form"] = "Edit form";
$lang["delete_form"] = "Delete form";

$lang["add_field"] = "Add field";
$lang["placeholder"] = "Placeholder";
$lang["required"] = "Required";

$lang["field_type"] = "Field Type";
$lang["preview"] = "Preview";

$lang["field_type_text"] = "Text";
$lang["field_type_textarea"] = "Textarea";
$lang["field_type_select"] = "Select";
$lang["field_type_multi_select"] = "Multi Select";

$lang["request_an_estimate"] = "Request an Estimate";
$lang["estimate_submission_message"] = "Your request has been submitted successfully!";

$lang["hold"] = "Hold";
$lang["processing"] = "Processing";
$lang["estimated"] = "Estimated";

$lang["add_estimate"] = "Add estimate";
$lang["edit_estimate"] = "Edit estimate";
$lang["delete_estimate"] = "Delete estimate";
$lang["valid_until"] = "Valid until";
$lang["estimate_date"] = "Estimate date";
$lang["accepted"] = "Accepted";
$lang["declined"] = "Declined";
$lang["sent"] = "Sent";
$lang["estimate_preview"] = "Estimate Preview";
$lang["estimate_to"] = "Estimate To";

$lang["can_access_estimates"] = "Can access estimates?";
$lang["request_an_estimate"] = "Request an estimate";
$lang["estimate_request_form_selection_title"] = "Please select a form from the following list to submit your request.";

$lang["mark_as_processing"] = "Mark as Processing";
$lang["mark_as_estimated"] = "Mark as Estimated";
$lang["mark_as_hold"] = "Mark as Hold";
$lang["mark_as_canceled"] = "Mark as Canceled";

$lang["mark_as_sent"] = "Mark as Sent";
$lang["mark_as_accepted"] = "Mark as Accepted";
$lang["mark_as_rejected"] = "Mark as Rejected";
$lang["mark_as_declined"] = "Mark as Declined";

$lang["estimate_request_received"] = "Estimate request received";
$lang["estimate_sent"] = "Estimate sent";
$lang["estimate_accepted"] = "Estimate accepted";
$lang["estimate_rejected"] = "Estimate rejected";

$lang["notification_estimate_request_received"] = "Submitted an estimate request";
$lang["notification_estimate_sent"] = "Sent an estimate";
$lang["notification_estimate_accepted"] = "Accepted an estimate";
$lang["notification_estimate_rejected"] = "Rejected an estimate";

$lang["clone_project"] = "Clone Project";
$lang["copy_tasks"] = "Copy tasks";
$lang["copy_project_members"] = "Copy project members";
$lang["copy_milestones"] = "Copy milestones";
$lang["copy_same_assignee_and_collaborators"] = "Copy same assignee and collaborators";
$lang["copy_tasks_start_date_and_deadline"] = "Copy tasks start date and deadline";
$lang["task_comments_will_not_be_included"] = "Tasks comments will not be included";
$lang["project_cloned_successfully"] = "The project has been cloned successfully";

$lang["search"] = "Search";
$lang["no_record_found"] = "No record found.";
$lang["excel"] = "Excel";
$lang["print_button_help_text"] = "Press escape when finished.";
$lang["are_you_sure"] = "Are you sure?";
$lang["file_upload_instruction"] = "Drag-and-drop documents here <br /> (or click to browse...)";
$lang["file_name_too_long"] = "Filename is too long.";
$lang["scrollbar"] = "Scrollbar";

$lang["short_sunday"] = "Sun";
$lang["short_monday"] = "Mon";
$lang["short_tuesday"] = "Tue";
$lang["short_wednesday"] = "Wed";
$lang["short_thursday"] = "Thu";
$lang["short_friday"] = "Fri";
$lang["short_saturday"] = "Sat";

$lang["min_sunday"] = "Su";
$lang["min_monday"] = "Mo";
$lang["min_tuesday"] = "Tu";
$lang["min_wednesday"] = "We";
$lang["min_thursday"] = "Th";
$lang["min_friday"] = "Fr";
$lang["min_saturday"] = "Sa";

$lang["january"] = "January";
$lang["february"] = "February";
$lang["march"] = "March";
$lang["april"] = "April";
$lang["may"] = "May";
$lang["june"] = "June";
$lang["july"] = "July";
$lang["august"] = "August";
$lang["september"] = "September";
$lang["october"] = "October";
$lang["november"] = "November";
$lang["december"] = "December";

$lang["short_january"] = "Jan";
$lang["short_february"] = "Feb";
$lang["short_march"] = "Mar";
$lang["short_april"] = "Apr";
$lang["short_may"] = "May";
$lang["short_june"] = "Jun";
$lang["short_july"] = "Jul";
$lang["short_august"] = "Aug";
$lang["short_september"] = "Sep";
$lang["short_october"] = "Oct";
$lang["short_november"] = "Nov";
$lang["short_december"] = "Dec";

/* Version 1.5 */

$lang["no_such_file_or_directory_found"] = "No such file or directory.";
$lang["gantt"] = "Gantt";
$lang["not_specified"] = "Not specified";
$lang["group_by"] = "Group by";
$lang["create_invoice"] = "Create Invoice";
$lang["include_all_items_of_this_estimate"] = "Include all items of this estimate";
$lang["edit_payment"] = "Edit payment";
$lang["disable_client_login"] = "Disable client login";
$lang["disable_client_signup"] = "Disable client signup";

$lang["chart"] = "Chart";
$lang["signin_page_background"] = "Signin page background";
$lang["show_logo_in_signin_page"] = "Show logo in signin page";
$lang["show_background_image_in_signin_page"] = "Show background image in signin page";

/* Version 1.6 */

$lang["more"] = "More";
$lang["custom"] = "Custom";
$lang["clear"] = "Clear";
$lang["expired"] = "Expired";
$lang["enable_attachment"] = "Enable attachment";
$lang["custom_fields"] = "Custom fields";
$lang["edit_field"] = "Edit field";
$lang["delete_field"] = "Delete field";
$lang["client_info"] = "Client info";
$lang["edit_expenses_category"] = "Edit expenses category";
$lang["eelete_expenses_category"] = "Delete expenses category";
$lang["empty_starred_projects"] = "To access your favorite projects quickly, please go to the project view and mark the star.";
$lang["empty_starred_clients"] = "To access your favorite clients quickly, please go to the client view and mark the star.";
$lang["download_zip_name"] = "documents";
$lang["invoice_prefix"] = "Invoice prefix";
$lang["invoice_style"] = "Invoice style";
$lang["delete_confirmation_message"] = " Are you sure? You won't be able to undo this action!";
$lang["left"] = "Left";
$lang["right"] = "Right";
$lang["currency_position"] = "Currency Position";
$lang["recipient"] = "Recipient";

$lang["new_message_sent"] = "New message sent";
$lang["message_reply_sent"] = "Message replied";
$lang["notification_new_message_sent"] = "Sent a message.";
$lang["notification_message_reply_sent"] = "Replied a message.";
$lang["invoice_payment_confirmation"] = "Invoice payment confirmation";
$lang["notification_invoice_payment_confirmation"] = "Payment received";

/* Version 1.7 */

$lang["client_can_create_projects"] = "Client can create projects?";
$lang["client_can_view_timesheet"] = "Client can view timesheet?";
$lang["client_can_view_gantt"] = "Client can view gantt?";
$lang["client_can_view_overview"] = "Client can view project overview?";
$lang["client_can_view_milestones"] = "Client can view milestones?";

$lang["items"] = "Items";
$lang["edit_item"] = "Edit item";
$lang["item_edit_instruction"] = "Note: The changes will not be affected on existing invoices, estimates or orders.";

$lang["recurring"] = "Recurring";
$lang["repeat_every"] = "Repeat every"; //Ex. repeat every 2 months
$lang["interval_days"] = "Day(s)";
$lang["interval_weeks"] = "Week(s)";
$lang["interval_months"] = "Month(s)";
$lang["interval_years"] = "Year(s)";
$lang["cycles"] = "Cycles";
$lang["recurring_cycle_instructions"] = "Recurring will be stopped after the number of cycles. Keep it blank for infinity.";
$lang["next_recurring_date"] = "Next recurring";
$lang["stopped"] = "Stopped";
$lang["past_recurring_date_error_message_title"] = "The selected bill date and repeat type returns a past date.";
$lang["past_recurring_date_error_message"] = "Next recurring date must be a future date. Please enter a future date.";
$lang["sub_invoices"] = "Sub invoices";

$lang["cron_job_required"] = "Cron Job is required for this action!";

$lang["recurring_invoice_created_vai_cron_job"] = "Recurring invoice created via Cron Job";
$lang["notification_recurring_invoice_created_vai_cron_job"] = "New invoice generated";

$lang["field_type_number"] = "Number";
$lang["show_in_table"] = "Show in table";
$lang["show_in_invoice"] = "Show in invoice";
$lang["visible_to_admins_only"] = "Visible to admins only";
$lang["hide_from_clients"] = "Hide from clients";
$lang["public"] = "Public";

$lang["help"] = "Help";
$lang["articles"] = "Articles";
$lang["add_article"] = "Add new article";
$lang["edit_article"] = "Edit article";
$lang["delete_article"] = "Delete article";
$lang["can_manage_help_and_knowledge_base"] = "Can manage help and knowledge base?";

$lang["how_can_we_help"] = "How can we help?";
$lang["help_page_title"] = "Internal Wiki";
$lang["search_your_question"] = "Search your question";
$lang["no_result_found"] = "No result found.";
$lang["sort"] = "Sort";
$lang["total_views"] = "Total views";

$lang["help_and_support"] = "Help & Support";
$lang["knowledge_base"] = "Knowledge base";

$lang["payment_success_message"] = "Your payment has been completed.";
$lang["payment_card_charged_but_system_error_message"] = "You card may be charged but we can't complete the process. Please contact to your system admin.";
$lang["card_payment_failed_error_message"] = "We can't process your payment right now, so please try again later.";

$lang["message_received"] = "Message received";
$lang["in_number_of_days"] = "In %s days"; //Ex. In 7 days
$lang["details"] = "Details";
$lang["summary"] = "Summary";
$lang["project_timesheet"] = "Project timesheet";

$lang["set_event_permissions"] = "Set event permissions";
$lang["disable_event_sharing"] = "Disable event sharing";
$lang["can_update_team_members_general_info_and_social_links"] = "Can update team member's general info and social links?";
$lang["can_manage_team_members_project_timesheet"] = "Can manage team member's project timesheet?";

$lang["cron_job"] = "Cron Job";
$lang["cron_job_link"] = "Cron Job link";
$lang["last_cron_job_run"] = "Last Cron Job run";
$lang["created_from"] = "Created from"; //Ex. Created from Invoice#1
$lang["recommended_execution_interval"] = "Recommended execution interval";

/* Version 1.8 */

$lang["integration"] = "Integration";
$lang["get_your_key_from_here"] = "Get your key from here:";
$lang["re_captcha_site_key"] = "Site key";
$lang["re_captcha_secret_key"] = "Secret key";

$lang["re_captcha_error-missing-input-secret"] = "reCAPTCHA secret is missing";
$lang["re_captcha_error-invalid-input-secret"] = "reCAPTCHA secret is not valid.";
$lang["re_captcha_error-missing-input-response"] = "Please select the reCAPTCHA.";
$lang["re_captcha_error-invalid-input-response"] = "The response parameter is invalid or malformed.";
$lang["re_captcha_error-bad-request"] = "The request is invalid or malformed.";
$lang["re_captcha_expired"] = "The reCAPTCHA has been expired. Please reload the page.";

$lang["yes_all_tickets"] = "Yes, all tickets";

$lang["can_manage_all_projects"] = "Can manage all projects";
$lang["show_most_recent_ticket_comments_at_the_top"] = "Show most recent ticket comments at the top";

$lang["new_event_added_in_calendar"] = "New event added in calendar";
$lang["notification_new_event_added_in_calendar"] = "Added a new event.";

$lang["todo"] = "To do";
$lang["add_a_todo"] = "Add a to do...";

/* Version 1.9 */

$lang["client_groups"] = "Client groups";
$lang["add_client_group"] = "Add client group";
$lang["edit_client_group"] = "Edit client group";
$lang["delete_client_group"] = "Delete client group";

$lang["ticket_prefix"] = "Ticket prefix";
$lang["add_a_task"] = "Add a task...";

$lang["add_task_status"] = "Add task status";
$lang["edit_task_status"] = "Edit task status";
$lang["delete_task_status"] = "Delete task status";

$lang["list"] = "List";
$lang["kanban"] = "Kanban";
$lang["priority"] = "Priority";
$lang["moved_up"] = "Moved Up";
$lang["moved_down"] = "Moved Down";
$lang["mark_project_as_hold"] = "Mark Project as Hold";

$lang["repeat"] = "Repeat";

$lang["hide_team_members_list"] = "Hide team members list?";

/* Version 2.0 */

$lang["summary_details"] = "Summary details";

$lang["chat"] = "Chat";
$lang["my_preferences"] = "My preferences";
$lang["show_push_notification"] = "Show push notification";
$lang["notification_sound_volume"] = "Notification sound volume";

$lang["project_reference_in_tickets"] = "Enable project reference";

$lang["hide_menus_from_client_portal"] = "Hide menus from client portal";
$lang["hidden_menus"] = "Hidden menus";

$lang["new_announcement_created"] = "New announcement created";
$lang["notification_new_announcement_created"] = "Created an announcement.";

$lang["month"] = "Month";
$lang["profit"] = "Profit";

$lang["invoice_due_reminder_before_due_date"] = "Invoice due reminder before due date";
$lang["send_due_invoice_reminder_notification_before"] = "Send due invoice reminder before due date";
$lang["send_invoice_overdue_reminder_after"] = "Send invoice overdue reminder after";
$lang["invoice_overdue_reminder"] = "Invoice overdue reminder";
$lang["recurring_invoice_creation_reminder"] = "Recurring invoice creation reminder";
$lang["send_recurring_invoice_reminder_before_creation"] = "Send recurring invoice reminder before creation";

$lang["notification_invoice_due_reminder_before_due_date"] = "Reminder: Invoice due.";
$lang["notification_invoice_overdue_reminder"] = "Reminder: Invoice overdue";
$lang["notification_recurring_invoice_creation_reminder"] = "An invoice will be generated soon.";

$lang["can_delete_leave_application"] = "Can delete leave application?";
$lang["no_of_decimals"] = "No. of decimals";

$lang["checklist"] = "Checklist";
$lang["delete_checklist_item"] = "Delete checklist item";

$lang["save_and_show"] = "Save & show";
$lang["total_leave_yearly"] = "Total Leave (Yearly)";

$lang["new_conversation"] = "New conversation";

$lang["enable_web_notification"] = "Enable web notification";
$lang["enable_email_notification"] = "Enable email notification";

/* Version 2.0.3 */

$lang["show_in_estimate"] = "Show in estimate";
$lang["mentioned_members"] = "Mentioned members";
$lang["all"] = "All";

$lang["confirmed"] = "Confirmed";
$lang["confirm"] = "Confirm";

$lang["confirmed_by"] = "Confirmed by";
$lang["confirm_event"] = "Confirm event";
$lang["reject_event"] = "Reject event";
$lang["event_status"] = "Event status";

$lang["specific_client_contacts"] = "Specific client contacts";
$lang["choose_client_contacts"] = "Choose client contacts";
$lang["invitations_sent"] = "The invitations has been sent.";

/* Version 2.1 */

$lang["add_new_dashboard"] = "Add new dashboard";
$lang["add_row"] = "Add row";

$lang["available_widgets"] = "Available Widgets";
$lang["your_selected_widgets_will_be_appear_here"] = "Your selected widgets will be appear here";
$lang["drag_and_drop_widgets_here"] = "Drag and drop widgets here";
$lang["no_more_widgets_available"] = "No more widgets available";
$lang["invalid_widget_access"] = "You don't have permission to access this widget";

$lang["dashboard_title"] = "Dashboard title";
$lang["edit_dashboard"] = "Edit dashboard";
$lang["edit_title"] = "Edit title";
$lang["default_dashboard"] = "Default dashboard";

$lang["widget"] = "Widget";
$lang["widgets"] = "Widgets";
$lang["add_widget"] = "Add widget";
$lang["edit_widget"] = "Edit widget";
$lang["delete_widget"] = "Delete widget";

$lang["content"] = "Content";
$lang["clock_in_out"] = "Clock in-out";
$lang["custom_widget_details"] = "Custom widget details";

$lang["total_projects"] = "Total projects";
$lang["total_invoices"] = "Total invoices";
$lang["total_payments"] = "Total payments";
$lang["total_due"] = "Total due";

$lang["show_title"] = "Show title";
$lang["show_border"] = "Show border";

$lang["all_tasks_kanban"] = "All tasks kanban";
$lang["todo_list"] = "Todo list";
$lang["open_projects_list"] = "Open Projects List";
$lang["starred_projects"] = "Starred Projects";
$lang["completed_projects"] = "Completed Projects";

$lang["new_tickets"] = "New Tickets";
$lang["closed_tickets"] = "Closed Tickets";

$lang["clocked_in_team_members"] = "Clocked in team members";
$lang["clocked_out_team_members"] = "Clocked out team members";
$lang["latest_online_client_contacts"] = "Latest online client contacts";
$lang["latest_online_team_members"] = "Latest online team members";
$lang["my_tasks_list"] = "My tasks list";

$lang["discount"] = "Discount";
$lang["discount_type"] = "Discount Type";
$lang["edit_discount"] = "Edit discount";
$lang["discount_amount"] = "Discount amount";
$lang["fixed_amount"] = "Fixed Amount";
$lang["before_tax"] = "Before Tax";
$lang["after_tax"] = "After Tax";

$lang["access_permission"] = "Access Permission";
$lang["setup"] = "Setup";
$lang["client_permissions"] = "Client permissions";

$lang["invoice_over_payment_error_message"] = "You can't pay more than your invoice due.";
$lang["account_already_exists_for_your_company_name"] = "Account already exists for your company name.";
$lang["personal_language"] = "Personal language";
$lang["no_messages_text"] = "You don't have any messages yet";
$lang["no_users_found"] = "No users found";

$lang["create_project"] = "Create project";

/* Version 2.2 */

$lang["imap_settings"] = "IMAP settings";
$lang["enable_email_piping"] = "Enable Email piping";
$lang["imap_host"] = "IMAP Host";
$lang["imap_port"] = "Port";
$lang["imap_ssl_enabled"] = "SSL Enabled";
$lang["please_upgrade_your_php_version"] = "Please upgrade your PHP Version for this operation.";
$lang["required_version"] = "Required Version";
$lang["email_piping_help_message"] = "Please make sure that, your IMap access is enabled.";

$lang["enable_rich_text_editor"] = "Enable rich text editor in comments/description";

$lang["show_assigned_tasks_only"] = "Show assigned tasks only";

$lang["batch_update"] = "Batch update";
$lang["cancel_selection"] = "Cancel selection";
$lang["select_status"] = "Select status";

$lang["add_multiple_tasks"] = "Add multiple tasks";
$lang["save_and_add_more"] = "Save & add more";
$lang["add_project_time"] = "Add project time";
$lang["add_to_do"] = "Add to do";
$lang["hide_menus_from_topbar"] = "Hide menus from topbar";
$lang["favorite_projects"] = "Favorite projects";
$lang["favorite_clients"] = "Favorite clients";
$lang["dashboard_customization"] = "Dashboard customization";
$lang["quick_add"] = "Quick add";

$lang["assign_to_me"] = "Assign to me";

$lang["favicon"] = "Favicon";

$lang["enable_google_drive_api_to_upload_file"] = "Enable Google Drive API to upload file";
$lang["drive_activation_help_message"] = "From now on, all files will be uploaded into Google Drive.";

$lang["mark_all_as_read"] = "Mark all as read";
$lang["marked_all_notifications_as_read"] = "Marked all notifications as read";

$lang["project_completed"] = "Project completed";
$lang["notification_project_completed"] = "Completed a project";

$lang["google_drive_client_id"] = "Client ID";
$lang["google_drive_client_secret"] = "Client secret";
$lang["get_your_app_credentials_from_here"] = "Get your app credentials from here:";
$lang["remember_to_add_this_url_in_authorized_redirect_uri"] = "Remember to add this url in Authorized redirect uri";
$lang["save_and_authorize"] = "Save & authorize";

$lang["preview_next_key"] = "Next (Right arrow key)";
$lang["preview_previous_key"] = "Previous (Left arrow key)";

$lang["filters"] = "Filters";

$lang["authorized"] = "Authorized";
$lang["unauthorized"] = "Unauthorized";

$lang["not_clocked_id_yet"] = "Not clocked in yet";

$lang["create_estimate_request"] = "Create estimate request";

$lang["in_last_number_of_days"] = "In last %s days";
$lang["in_last_number_of_month"] = "In last %s month";
$lang["in_last_number_of_months"] = "In last %s months";

$lang["pusher_app_id"] = "App ID";
$lang["pusher_key"] = "Key";
$lang["pusher_secret"] = "Secret";
$lang["pusher_cluster"] = "Cluster";
$lang["enable_push_notification"] = "Enable push notification";
$lang["push_notification"] = "Push notification";
$lang["disable_push_notification"] = "Disable push notification";

$lang["unknown_client"] = "Unknown client";

$lang["income_expenses_widget_help_message"] = "This report is only usable if you are using single currency.";

$lang["assign_myself_in_this_ticket"] = "Assign myself in this ticket";

$lang["create_new_task"] = "Create new task";

$lang["default_due_date_after_billing_date"] = "Default due date after billing date";

$lang["field_type_external_link"] = "External link";

$lang["total_days"] = "Total days";

$lang["my_timesheet"] = "My timesheet";
$lang["all_timesheets"] = "All timesheets";
$lang["my_timesheet_statistics"] = "My timesheet statistics";
$lang["all_timesheets_statistics"] = "All timesheets statistics";

$lang["no_field_has_selected"] = "No field has selected!";

$lang["imap_help_message_1"] = "You can setup an email address to create the tickets automatically when you receive any emails at that address.";
$lang["imap_help_message_2"] = "Please note that, the system will create tickets based on the unread emails. After creating the ticket, the emails will be marked as read. To get the replies in the same tickets, the system will check the ticket ID in the email subject. If there is no ticket ID in the subject, that will be considered as a new ticket. You can setup the email subject from the";
$lang["imap_error_credentials_message"] = "Error! Can't connect with the imap using the credentials.";

$lang["client_message_own_contacts"] = "Client can send/receive message to/from own contacts?";

$lang["print_invoice"] = "Print invoice";

$lang["mark_invoice_as_cancelled"] = "Mark as cancelled";
$lang["cancelled"] = "Cancelled";
$lang["cancelled_at"] = "Cancelled at";
$lang["cancelled_by"] = "Cancelled by";

/* Version 2.3 */

$lang["test_push_notification"] = "Test push notification";
$lang["notification_test_push_notification"] = "Great! Push notification looks OK.";
$lang["push_notification_error_message"] = "Error! Can't connect with the Pusher using the credentials.";
$lang["clone_estimate"] = "Clone Estimate";

$lang["import_clients"] = "Import clients";
$lang["download_sample_file"] = "Download sample file";

$lang["estimate_settings"] = "Estimate Settings";
$lang["estimate_logo"] = "Estimate Logo";
$lang["estimate_color"] = "Estimate Color";
$lang["initial_number_of_the_estimate"] = "Initial number of the estimate";
$lang["the_estimates_id_must_be_larger_then_last_estimate_id"] = "The estimates ID must be larger than the last estimate ID.";

$lang["send_to_client"] = "Send to client";
$lang["estimate_sent_message"] = "The estimate has been sent!";
$lang["send_estimate_bcc_to"] = "When sending estimate to client, send BCC to";

$lang["task_settings"] = "Task settings";
$lang["enable_recurring_option_for_tasks"] = "Enable recurring option for tasks";
$lang["past_recurring_date_error_message_title_for_tasks"] = "The selected start date and repeat type returns a past date.";
$lang["recurring_task_created_via_cron_job"] = "Recurring task created via Cron Job";
$lang["notification_recurring_task_created_via_cron_job"] = "New task created";
$lang["repeat_type"] = "Repeat type";
$lang["lead_status"] = "Lead status";
$lang["add_lead_status"] = "Add lead status";
$lang["edit_lead_status"] = "Edit lead status";
$lang["delete_lead_status"] = "Delete lead status";
$lang["owner"] = "Owner";
$lang["make_client"] = "Convert to client";
$lang["client_contacts"] = "Client contacts";
$lang["lead_contacts"] = "Lead contacts";
$lang["add_a_lead"] = "Add a lead";
$lang["source"] = "Source";
$lang["lead_source"] = "Lead source";
$lang["add_lead_source"] = "Add lead source";
$lang["edit_lead_source"] = "Edit lead source";
$lang["delete_lead_source"] = "Delete lead source";
$lang["custom_field_migration"] = "Custom field migration";
$lang["merge_custom_fields"] = "Merge custom fields";
$lang["do_not_merge"] = "Do not merge";
$lang["merge_custom_fields_help_message"] = "If there is any similar custom fields exists for %s, this values will be added to those. Otherwise, this will create new custom fields for %s and add values to those.";
$lang["lead_created"] = "Lead created";
$lang["notification_lead_created"] = "Created a new lead.";
$lang["client_created_from_lead"] = "Client created from lead";
$lang["notification_client_created_from_lead"] = "Converted a lead to client.";
$lang["project_deadline"] = "Project deadline";
$lang["task_deadline"] = "Task deadline";
$lang["event_type"] = "Event type";
$lang["delete_estimate_form"] = "Delete estimate form";
$lang["calendar_event_modified"] = "Calendar event modified";
$lang["notification_calendar_event_modified"] = "Modified an event.";

$lang["there_has_leads_with_this_status"] = "There has leads with this status";
$lang["lead_created_at"] = "Lead created at";
$lang["past_lead_information"] = "Past lead information";
$lang["last_status"] = "Last status";
$lang["migrated_to_client_at"] = "Migrated to client at";
$lang["edit_estimate_form"] = "Edit estimate form";

$lang["please_upload_a_excel_file"] = "Please upload a excel file.";
$lang["back"] = "Back";

$lang["import_client_error_header"] = "There has an invalid header. The indicated field should be <b>%s</b>.";
$lang["import_client_error_company_name_field_required"] = "Company name field is required.";
$lang["import_client_error_contact_name"] = "Contact first name and last name is both required to add a client contact.";
$lang["import_client_error_contact_email"] = "Contact email is required and should be unique to add a client contact.";
$lang["error"] = "Error";
$lang["contact_first_name"] = "Contact first name";
$lang["contact_last_name"] = "Contact last name";
$lang["contact_email"] = "Contact email";

$lang["clone_invoice"] = "Clone Invoice";
$lang["copy_items"] = "Copy items";
$lang["copy_discount"] = "Copy discount";

$lang["clone_task"] = "Clone task";
$lang["copy_checklist"] = "Copy checklist";

$lang["auto_assign_estimate_request_to"] = "Auto assign estimate request to";

$lang["email_template_variable"] = "Email template variable";
$lang["example_variable_name"] = "Example_variable_name";

$lang["imap_extension_error_help_message"] = "You don't have IMAP extension in your server. Please install the extension for this action.";

$lang["initial_number_of_the_invoice"] = "Initial number of the invoice";
$lang["the_invoices_id_must_be_larger_then_last_invoice_id"] = "The invoices ID must be larger than the last invoice ID.";

$lang["client_dashboard_help_message"] = "This will be the default dashboard for all clients. Please note that, the information you're seeing here in the widgets, isn't any actual information of clients.";

$lang["send_to_lead"] = "Send to lead";
$lang["lead"] = "Lead";
$lang["leads"] = "Leads";
$lang["add_lead"] = "Add lead";
$lang["edit_lead"] = "Edit lead";
$lang["delete_lead"] = "Delete lead";
$lang["lead_details"] = "Lead details";
$lang["can_access_leads_information"] = "Can access lead's information?";
$lang["lead_info"] = "Lead info";

$lang["send_task_reminder_on_the_day_of_deadline"] = "Send task reminder on the day of deadline";
$lang["send_task_deadline_pre_reminder"] = "Send task deadline pre reminder";
$lang["send_task_deadline_overdue_reminder"] = "Send task deadline overdue reminder";

$lang["project_task_deadline_reminder"] = "Project task deadline reminder";

$lang["project_task_deadline_pre_reminder"] = "Project task deadline pre reminder";
$lang["project_task_deadline_overdue_reminder"] = "Project task deadline overdue reminder";
$lang["project_task_reminder_on_the_day_of_deadline"] = "Project task reminder on the day of deadline";

$lang["notification_project_task_deadline_pre_reminder"] = "Reminder: Some tasks needs to be finished soon.";
$lang["notification_project_task_deadline_overdue_reminder"] = "Reminder: Task's deadline overdue.";
$lang["notification_project_task_reminder_on_the_day_of_deadline"] = "Reminder: Some tasks needs to be finished today.";

$lang["mark_as_public"] = "Mark as public";
$lang["note_details"] = "Note details";
$lang["public_note_by"] = "Public note by";
$lang["marked_as_public"] = "Marked as public";

$lang["client_can_view_activity"] = "Client can view project activity";

$lang["event_settings"] = "Event settings";
$lang["enable_google_calendar_api"] = "Enable Google calendar API";
$lang["google_calendar_settings"] = "Google calendar settings";

$lang["your_calendar_ids"] = "Your Calendar IDs";
$lang["calendar_id"] = "Calendar ID";
$lang["now_every_user_can_integrate_with_their_google_calendar"] = "Now every user can integrate with their Google calendar.";
$lang["calendar_ids_help_message"] = "You'll get your own calendar events always. This is for other special calendars (Like Holidays Calendar).";

$lang["google_client_id"] = "Client ID";
$lang["google_client_secret"] = "Client secret";
$lang["integrate_with_google_calendar"] = "Integrate with Google calendar";
$lang["google_calendar_event"] = "Google Calendar event";

$lang["mark_as_public_help_message"] = "You can't make this note as private again.";

$lang["google_calendar_help_message"] = "You'll get your Google Calendar events by the run of Cron job. And any add/modification of your local events will effect your Google calendar instantly.";

/* Version 2.4 */

$lang["footer"] = "Footer";
$lang["footer_description_message"] = "This footer will be visible on all public pages.";
$lang["estimate_footer"] = "Estimate Footer";
$lang["enable_footer"] = "Enable footer";
$lang["footer_menus"] = "Footer menus";
$lang["footer_copyright_text"] = "Copyright text";
$lang["edit_footer_menu"] = "Edit footer menu";

$lang["menu_name"] = "Menu name";
$lang["task_point_range"] = "Task point range";

$lang["gdpr"] = "GDPR";
$lang["enable_gdpr"] = "Enable GDPR";
$lang["allow_clients_to_export_their_data"] = "Allow clients to export their data";
$lang["export_my_data"] = "Export my data";

$lang["clients_can_request_account_removal"] = "Clients can request account removal";
$lang["i_want_to_remove_my_account"] = "I want to remove my account";
$lang["client_contact_requested_account_removal"] = "Client contact requested account removal";
$lang["notification_client_contact_requested_account_removal"] = "Requested account removal.";
$lang["show_terms_and_conditions_in_client_signup_page"] = "Show Terms and Conditions in client signup page";
$lang["i_accept_the_terms_and_conditions"] = "I accept the";

$lang["apply"] = "Apply";
$lang["applied"] = "Applied";
$lang["export"] = "Export";

$lang["pages"] = "Pages";
$lang["add_page"] = "Add page";
$lang["delete_page"] = "Delete page";
$lang["page_url_cant_duplicate"] = "Page URL can't duplicate.";

$lang["sub_tasks"] = "Sub tasks";
$lang["sub_task"] = "Sub task";
$lang["create_a_sub_task"] = "Create a sub task";
$lang["create"] = "Create";
$lang["parent_task"] = "Parent task";

$lang["this_task_blocked_by"] = "This task blocked by";
$lang["this_task_blocking"] = "This task blocking";
$lang["add_dependency"] = "Add dependency";
$lang["blocked_by"] = "Blocked by";
$lang["blocking"] = "Blocking";
$lang["blocked"] = "Blocked";
$lang["dependency"] = "Dependency";

$lang["estimate_request_settings"] = "Estimate request settings";
$lang["hidden_client_fields_on_public_estimate_requests"] = "Hide fields from public estimate request forms";
$lang["hidden_client_fields"] = "Hidden client fields";

$lang["account"] = "Account";
$lang["common"] = "Common";

$lang["tax_deducted_at_source"] = "TDS";
$lang["auto_close_ticket_after"] = "Auto close ticket after"; //after x days
$lang["disable_user_invitation_option_by_clients"] = "Disable user invitation option by clients";
$lang["create_tickets_only_by_registered_emails"] = "Create tickets only by registered emails";
$lang["icon"] = "Icon";
$lang["help_articles"] = "Help articles";
$lang["help_categories"] = "Help categories";
$lang["knowledge_base_articles"] = "KB articles";
$lang["knowledge_base_categories"] = "KB categories";

$lang["rtl"] = "RTL";

$lang["disable_editing_by_clients"] = "Disable editing by clients";

$lang["client_left_menu"] = "Left menu";
$lang["left_menu_for_client"] = "Left menu for client";
$lang["left_menu_setting_help_message_for_client"] = "This will be the default left menu for clients. Please note that, the menu items will be distributed as per client contact's permissions.";
$lang["available_menu_items"] = "Available menu items";
$lang["drag_and_drop_items_here"] = "Drag and drop items here";
$lang["no_more_items_available"] = "No more items available";
$lang["left_menu_preview_message"] = "Hit save button to see preview.";
$lang["left_menu_setting_help_message"] = "This will be the default left menu for team members. Please note that, the menu items will be distributed as per user's permission.";

$lang["draft_invoices"] = "Draft invoices";
$lang["draft_invoices_total"] = "Draft Invoices Total";
$lang["draft_invoices_value"] = "Draft invoices value";

$lang["gdpr_terms_and_conditions_link"] = "Terms and Conditions URL";
$lang["gdpr_terms_and_conditions"] = "Terms and Conditions";
$lang["removal_request_pending"] = "Removal Request Pending";

$lang["client_access_files_help_message"] = "The files which are located in client details view in the Files tab.";
$lang["estimate_request_name_email_error_message"] = "Email can't be shown without first name and last name.";

$lang["slug"] = "Slug";
$lang["add_assignee"] = "Add assignee";

$lang["client_can_pay_invoice_without_login"] = "Client can pay invoices without login";
$lang["client_can_pay_invoice_without_login_help_message"] = "Please add the PUBLIC_PAY_INVOICE_URL in the invoice email notification template.";

$lang["link_to_existing_client"] = "Link to existing client";
$lang["link_to_new_client"] = "Link to new client";

$lang["client_can_view_files"] = "Client can view files?";
$lang["client_can_add_files"] = "Client can add files?";
$lang["client_can_edit_projects"] = "Client can edit projects?";

$lang["view_pdf"] = "View PDF";

$lang["add_new_task"] = "Add new task";
$lang["disable_keyboard_shortcuts"] = "Disable keyboard shortcuts";
$lang["keyboard_shortcuts_info"] = "Keyboard shortcuts info";
$lang["edit_shortcuts"] = "Edit shortcuts";

$lang["pending_leave_approval"] = "Pending leave approval";
$lang["add_attachment"] = "Add Attachment";

$lang["hidden_topbar_menus"] = "Hidden topbar menus";

$lang["make_previous_items_sub_menu"] = "Make/remove sub menu of the previous item";
$lang["add_menu_item"] = "Add menu item";
$lang["url"] = "URL";

$lang["show_theme_color_changer"] = "Show theme color changer";
$lang["default_theme_color"] = "Default theme color";
$lang["left_menu"] = "Left menu";
$lang["client_assigned_contacts"] = "Assigned client contacts";
$lang["timesheet_settings"] = "Timesheet Settings";
$lang["users_can_start_multiple_timers_at_a_time"] = "Users can start multiple timers at a time";

$lang["delete_expenses_category"] = "Delete expenses category";

/* Version 2.5 */

$lang["code_reference"] = "Code Reference";

$lang["commit_url"] = "Commit url";
$lang["new_commits"] = "New commits";
$lang["new_commit"] = "New commit";
$lang["pushed_by"] = "Pushed by";
$lang["committed_by"] = "Committed by";
$lang["add_webhook_in_your_repository_at"] = "Add webhook in your repository: ";
$lang["webhook_listener_link"] = "Webhook listener link";
$lang["enable_bitbucket_commit_logs_in_tasks"] = "Enable bitbucket commit logs in tasks";
$lang["bitbucket_info_text"] = "To link the commits with tasks, there should be a # and task ID at the end of each commit messages. Ex: This is a commit of Task #10.";

$lang["bitbucket_push_received"] = "Bitbucket notification received";
$lang["notification_bitbucket_push_received"] = "Bitbucket notification received.";

$lang["hour_log_time_error_message"] = "Please input hour(s) in correct format.";
$lang["set_message_permissions"] = "Set message permissions";
$lang["cant_send_any_messages"] = "Can't send any messages";
$lang["can_send_messages_to_specific_members_or_teams"] = "Can send messages to specific members or teams:";

$lang["embed"] = "Embed";
$lang["copy"] = "Copy";

$lang["estimate_prefix"] = "Estimate prefix";

$lang["likes"] = "Likes";

$lang["pusher"] = "Pusher";
$lang["enable_chat_via_pusher"] = "Enable chat via pusher";

$lang["tasks_list"] = "Tasks List";
$lang["tasks_kanban"] = "Tasks Kanban";
$lang["set_project_tab_order"] = "Set project tab order";
$lang["project_tab_order"] = "Project tab order";
$lang["project_tab_order_help_message"] = "Please note that, this tabs will show as per user's permissions.";
$lang["project_tab_order_help_message_of_client"] = "Please note that, this tabs will show as per client contact's permissions.";
$lang["client_projects"] = "Projects";

$lang["ticket_assigned"] = "Ticket assigned";
$lang["notification_ticket_assigned"] = "Assigned a ticket to %s";

$lang["disable_access_favorite_project_option_for_clients"] = "Disable access favorite project option for clients";
$lang["disable_editing_left_menu_by_clients"] = "Disable editing left menu by clients";
$lang["disable_topbar_menu_customization"] = "Disable topbar menu customization";
$lang["disable_dashboard_customization_by_clients"] = "Disable dashboard customization by clients";

$lang["task_start_date"] = "Task start date";
$lang["project_start_date"] = "Project start date";
$lang["show_on_kanban_card"] = "Show on kanban card";

$lang["original_expense"] = "Original expense";
$lang["expense_details"] = "Expense details";

$lang["read_only"] = "Read only";

$lang["internal_use_only"] = "Internal use only";
$lang["visible_to_team_members_only"] = "Visible to team members only";
$lang["visible_to_clients_only"] = "Visible to clients only";

$lang["open_in_new_tab"] = "Open in new tab";

$lang["client_can_delete_own_files_in_project"] = "Client can delete own files in project";

$lang["enable_slack"] = "Enable slack";
$lang["get_the_webhook_url_of_your_app_from_here"] = "Get the Webhook URL of your App from here:";
$lang["slack_webhook_url"] = "Webhook URL";
$lang["send_a_test_message"] = "Send a test message";
$lang["notification_test_slack_notification"] = "This is a demo message.";
$lang["slack_notification_error_message"] = "Error! Can't connect with the Slack using the credentials.";
$lang["dont_send_any_project_related_notifications_to_this_channel"] = "Don't send any project related notifications to this channel";
$lang["save_and_send_a_test_message"] = "Save & send a test message";

$lang["copy_sub_tasks"] = "Copy sub tasks";

$lang["can_update_only_assigned_tasks_status"] = "Can update only assigned tasks status";

$lang["import_leads"] = "Import leads";
$lang["import_lead_error_contact_name"] = "Contact first name and last name is both required to add a lead contact.";

$lang["deadline_must_be_equal_or_greater_than_start_date"] = "Deadline must be equal or greater than Start date.";

$lang["enable_github_commit_logs_in_tasks"] = "Enable github commit logs in tasks";
$lang["github_push_received"] = "GitHub notification received";
$lang["notification_github_push_received"] = "GitHub notification received.";

$lang["invalid_calendar_id_error_message"] = "This Calendar ID isn't valid or you don't have permission to access this Calendar";
$lang["total_clients"] = "Total clients";
$lang["total_contacts"] = "Total contacts";

$lang["message_sending_error_message"] = "This user doesn't have permission to send message to you. That's why you also can't send message!";

$lang["days_view"] = "Days view";
$lang["weeks_view"] = "Weeks view";
$lang["months_view"] = "Months view";

$lang["move_all_tasks_to_to_do"] = "Move all tasks to To Do";

$lang["started"] = "Started";

$lang["weekends"] = "Weekends";

$lang["invited_client_contact_signed_up"] = "Invited client contact signed up";
$lang["notification_invited_client_contact_signed_up"] = "Invited client contact signed up.";

$lang["ticket_templates"] = "Ticket templates";
$lang["ticket_template"] = "Ticket template";
$lang["tickets_list"] = "Tickets list";
$lang["add_template"] = "Add template";
$lang["edit_template"] = "Edit template";
$lang["insert_template"] = "Insert template";
$lang["private_template"] = "Private template";

$lang["requested_by"] = "Requested by";

$lang["create_new_projects_automatically_when_estimates_gets_accepted"] = "Create new projects automatically when estimates gets accepted";

$lang["typing"] = "Typing";

$lang["new_client_greetings"] = "New client greetings";

$lang["timeline_post_commented"] = "Timeline post commented";
$lang["post_creator"] = "Post creator";
$lang["notification_timeline_post_commented"] = "Commented on a post.";
$lang["created_a_new_post"] = "Created a new post";
$lang["notification_created_a_new_post"] = "Created a new post.";

$lang["verify_email_before_client_signup"] = "Verify email before client signup";
$lang["input_your_email"] = "Input your email";
$lang["verify_email"] = "Client email verification";
$lang["please_continue_your_signup_process"] = "Please continue your signup process.";
$lang["get_started"] = "Get Started";

$lang["manage_labels"] = "Manage labels";

$lang["timesheet"] = "Timesheet";
$lang["users_can_input_only_total_hours_instead_of_period"] = "Users can input only total hours instead of period";
$lang["timesheet_hour_input_help_message"] = "Ex: 1h 20m";

$lang["template"] = "Template";
$lang["template_details"] = "Template details";

$lang["label_existing_error_message"] = "This label already in use. It can't be deleted.";

/* Version 2.6 */

$lang["paytm_checksum_hash_error_message"] = "Couldn't generate Checksum Hash with your credentials.";

$lang["testing_environment"] = "Testing environment";

$lang["auto_reply_to_tickets"] = "Auto reply to tickets";

$lang["total_time_logged"] = "Total time logged";
$lang["total_duration"] = "Total duration";

$lang["please_upload_valid_image_files"] = "Please upload valid image files.";
$lang["upload_image"] = "Upload Image";
$lang["item_details"] = "Item details";
$lang["item_image_sorting_help_message"] = "First image will be the default image.";
$lang["show_in_client_portal"] = "Show in client portal";
$lang["showing_in_client_portal"] = "Showing in client portal";
$lang["add_to_cart"] = "Add to cart";
$lang["item_empty_message"] = "No items found!";

$lang["order"] = "Order";
$lang["orders"] = "Orders";
$lang["no_items_text"] = "You don't have any items in your shopping cart!";
$lang["process_order"] = "Process Order";
$lang["place_order"] = "Place order";
$lang["edit_item"] = "Edit item";
$lang["store"] = "Store";
$lang["client_can_access_store"] = "Client can access store?";
$lang["added_to_cart"] = "Added to cart";
$lang["can_access_orders"] = "Can access orders?";

$lang["order_settings"] = "Order settings";
$lang["order_logo"] = "Order logo";
$lang["order_prefix"] = "Order prefix";
$lang["order_color"] = "Order color";
$lang["initial_number_of_the_order"] = "Initial number of the order";
$lang["the_orders_id_must_be_larger_then_last_order_id"] = "The orders ID must be larger than the last order ID.";
$lang["order_footer"] = "Order footer";

$lang["order_status"] = "Order status";
$lang["edit_order_status"] = "Edit order status";
$lang["add_order_status"] = "Add order status";
$lang["delete_order_status"] = "Delete order status";
$lang["there_has_orders_with_this_status"] = "There has orders with this status";
$lang["orders_list"] = "Orders list";
$lang["sales"] = "Sales";
$lang["order_date"] = "Order date";
$lang["edit_order"] = "Edit order";
$lang["delete_order"] = "Delete order";
$lang["show_in_order"] = "Show in order";
$lang["order_preview"] = "Order preview";
$lang["order_from"] = "Order from";
$lang["add_order"] = "Add order";

$lang["process_order_info_message"] = "You are about to create the order. Please check details before submitting.";

$lang["order_creator_contact"] = "Order creator contact";

$lang["create_estimate"] = "Create Estimate";
$lang["include_all_items_of_this_order"] = "Include all items of this order";

$lang["new_order_received"] = "New order received";
$lang["notification_new_order_received"] = "New order received.";

$lang["order_status_updated"] = "Order status updated";
$lang["notification_order_status_updated"] = "Order status has been updated.";

$lang["add_more_items"] = "Add more items";

$lang["yes_only_own_leads"] = "Yes, only own leads";
$lang["yes_all_leads"] = "Yes, all leads";

$lang["yes_only_own_clients"] = "Yes, only own clients";
$lang["yes_all_clients"] = "Yes, all clients";

$lang["recently_updated"] = "Recently updated";
$lang["recently_moved_to"] = "Recently moved to";

$lang["recently_commented"] = "Recently commented";
$lang["mentioned_me"] = "Mentioned me";
$lang["recently_mentioned_me"] = "Recently mentioned me";
$lang["in"] = "In";
$lang["recently_meaning"] = "Recently meaning";

$lang["quick_filters"] = "Quick filters";

$lang["has_open_projects"] = "Has open projects";
$lang["has_completed_projects"] = "Has completed projects";
$lang["has_any_hold_projects"] = "Has any hold projects";

$lang["has_unpaid_invoices"] = "Has unpaid invoices";
$lang["has_overdue_invoices"] = "Has overdue invoices";
$lang["has_partially_paid_invoices"] = "Has partially paid invoices";
$lang["assignee"] = "Assignee";

$lang["upload_and_crop"] = "Upload and crop";

$lang["active_members_on_projects"] = "Active members on projects";

/* Version 2.6.1 */

$lang["open_tickets_list"] = "Open tickets list";

$lang["login_attempt_failed"] = "Login attempt failed";
$lang["profile_image_error_message"] = "The image should be 200x200px.";

$lang["re_captcha_info_text"] = "Before you logout, please open a new browser and make sure the reCaptcha is working.";
$lang["yes_assigned_tickets_only"] = "Yes, assigned tickets only";
$lang["no_such_custom_field_found"] = "No such custom field found.";
$lang["open_in_google_calendar"] = "Open in google calendar";

$lang["enable_embedded_form_to_get_tickets"] = "Enable embedded form to get tickets";
$lang["submit_your_request"] = "Submit your request";
$lang["submit"] = "Submit";
$lang["ticket_submission_message"] = "Your ticket has been submitted successfully!";
$lang["your_email"] = "Your email";
$lang["your_name"] = "Your name";

$lang["item_categories"] = "Item categories";
$lang["edit_items_category"] = "Edit items category";
$lang["delete_items_category"] = "Delete items category";

$lang["create_recurring_tasks_before"] = "Create recurring tasks before";
$lang["create_new_order"] = "Create new order";
$lang["find_more_items"] = "Find more items";

/* Version 2.8 */

$lang["reports"] = "Reports";

$lang["yes_all_estimates"] = "Yes, all estimates";
$lang["yes_only_own_estimates"] = "Yes, only own estimates";

$lang["proposal"] = "Proposal";
$lang["proposals"] = "Proposals";
$lang["can_access_proposals"] = "Can access proposals?";
$lang["show_in_proposal"] = "Show in proposal";
$lang["proposal_date"] = "Proposal date";
$lang["edit_proposal"] = "Edit proposal";
$lang["delete_proposal"] = "Delete proposal";
$lang["proposal_sent_message"] = "The proposal has been sent!";
$lang["add_proposal"] = "Add proposal";
$lang["proposal_preview"] = "Proposal preview";
$lang["clone_proposal"] = "Clone proposal";
$lang["proposal_to"] = "Proposal to";
$lang["proposal_settings"] = "Proposal settings";
$lang["proposal_prefix"] = "Proposal prefix";
$lang["proposal_color"] = "Proposal color";
$lang["send_proposal_bcc_to"] = "When sending proposal to client, send BCC to";
$lang["initial_number_of_the_proposal"] = "Initial number of the proposal";
$lang["the_proposals_id_must_be_larger_then_last_proposal_id"] = "The proposals ID must be larger than the last proposal ID.";
$lang["proposal_sent"] = "Proposal sent";
$lang["notification_proposal_sent"] = "Sent a proposal";
$lang["proposal_accepted"] = "Proposal accepted";
$lang["notification_proposal_accepted"] = "Accepted a proposal";
$lang["proposal_rejected"] = "Proposal rejected";
$lang["notification_proposal_rejected"] = "Rejected a proposal";
$lang["create_estimate"] = "Create Estimate";
$lang["include_all_items_of_this_proposal"] = "Include all items of this proposal";
$lang["proposal_view"] = "Proposal view";
$lang["accept_proposal"] = "Accept proposal";
$lang["reject_proposal"] = "Reject proposal";
$lang["proposal_accepted_message"] = "You've successfully accepted this proposal!";

$lang["set_timeline_permissions"] = "Set timeline permissions";
$lang["cant_see_the_timeline"] = "Can't see the Timeline";
$lang["can_see_timeline_posts_from_specific_members_or_teams"] = "Can see timeline posts from specific members or teams:";

$lang["localization"] = "Localization";
$lang["localization_settings"] = "Localization Settings";
$lang["main_task"] = "Main task";

$lang["select_all"] = "Select all";
$lang["unselect_all"] = "Unselect all";

$lang["plugins"] = "Plugins";
$lang["install_plugin"] = "Install plugin";
$lang["please_upload_a_zip_file"] = "Please upload a zip file.";
$lang["install"] = "Install";
$lang["installed"] = "Installed";
$lang["activate"] = "Activate";
$lang["activated"] = "Activated";
$lang["deactivate"] = "Deactivate";
$lang["deactivated"] = "Deactivated";
$lang["the_required_files_missing"] = "The required files are missing.";
$lang["this_plugin_is_already_installed"] = "This plugin is already installed.";
$lang["version"] = "Version";
$lang["by"] = "By";
$lang["visit_plugin_site"] = "Visit plugin site";

$lang["can_manage_team_members_job_information"] = "Can manage team member's job information?";

$lang["add_filter"] = "Add filter";
$lang["specific_client_groups"] = "Specific client groups";
$lang["choose_client_groups"] = "Choose client groups";

$lang["checklist_template"] = "Checklist Template";
$lang["add_checklist_template"] = "Add checklist template";
$lang["edit_checklist_template"] = "Edit checklist template";
$lang["delete_checklist_template"] = "Delete checklist template";
$lang["select_from_template"] = "Select from template";
$lang["type_new_item"] = "Type new item";

$lang["conversion_rate"] = "Conversion rate";

$lang["all_tasks"] = "All tasks";
$lang["user_roles"] = "User Roles";
$lang["edit_user_role"] = "Edit user role";

$lang["total_leads"] = "Total leads";

$lang["copy_link"] = "Copy link";
$lang["copy_comment_link"] = "Copy comment link";

$lang["pin_comment"] = "Pin comment";
$lang["unpin_comment"] = "Unpin comment";
$lang["pinned_comments"] = "Pinned comments";

$lang["reply_from_this_comment"] = "Reply from this comment <br />";

$lang["project_files"] = "Project files";
$lang["edit_files"] = "Edit files";

$lang["invoice_manual_payment_added"] = "Invoice manual payment added";
$lang["notification_invoice_manual_payment_added"] = "Added a manual payment.";

$lang["save_as_note"] = "Save as note";
$lang["client_will_not_see_any_notes"] = "Client will not see any notes.";

$lang["prospects"] = "Prospects";

$lang["proposal_editor"] = "Proposal Editor";
$lang["proposal_templates"] = "Proposal templates";
$lang["add_proposal_template"] = "Add proposal template";
$lang["edit_proposal_template"] = "Edit proposal template";
$lang["delete_proposal_template"] = "Delete proposal template";
$lang["use_template_from"] = "Use template from";
$lang["print_proposal"] = "Print proposal";
$lang["proposal_template_inserting_instruction"] = "You'll lost all unsaved changes by inserting a template.";

$lang["default"] = "Default";

$lang["encryption"] = "Encryption";
$lang["imap_encryption_help_message"] = "Different server could work with different configuration. If you see any problem to connect with IMAP, please check with different options.";

$lang["administration_permissions"] = "Administration permissions";
$lang["can_manage_all_kinds_of_settings"] = "Can manage all kinds of settings";
$lang["can_manage_user_role_and_permissions"] = "Can manage user role and permissions";
$lang["can_add_or_invite_new_team_members"] = "Can add/invite new team members";

$lang["add_signature_option_on_accepting_proposal"] = "Add signature option on accepting proposal";
$lang["accept"] = "Accept";
$lang["signer_info"] = "Signer info";
$lang["default_template"] = "Default template";
$lang["change_template"] = "Change template";
$lang["this_variable_is_unsupported"] = "This variable is unsupported";

$lang["plugin_deletion_alert_message"] = "All records and files also will be deleted!";
$lang["plugin_requires_at_least_error_message"] = "This plugin requires at least %s version.";
$lang["plugin_supports_at_most_error_message"] = "This plugin supports at most %s version.";
$lang["no_update_hook_found"] = "No update hook found!";
$lang["indexed"] = "Indexed";

$lang["save_and_continue"] = "Save & continue";
$lang["add_new_project_member"] = "Add new project member";

$lang["field_type_time"] = "Time";
$lang["client_can_assign_tasks"] = "Client can assign tasks?";
$lang["can_create_lead_from_public_form"] = "Can create lead from public form";
$lang["lead_html_form_code"] = "Lead creation HTML form code";

$lang["enable_comments_on_estimates"] = "Enable comments on estimates";
$lang["show_most_recent_estimate_comments_at_the_top"] = "Show most recent estimate comments at the top";
$lang["estimate_commented"] = "Estimate commented";
$lang["estimate_creator"] = "Estimate creator";
$lang["notification_estimate_commented"] = "Commented on an estimate.";

$lang["contacts_logged_in_today"] = "Contacts logged in today";
$lang["contacts_logged_in_last_seven_days"] = "Contacts logged in last 7 days";

$lang["clients_has_unpaid_invoices"] = "Clients has unpaid invoices";
$lang["clients_has_partially_paid_invoices"] = "Clients has partially paid invoices";
$lang["clients_has_overdue_invoices"] = "Clients has overdue invoices";

$lang["of_total_clients"] = "of total clients";

$lang["has_canceled_projects"] = "Has canceled projects";
$lang["clients_has_open_projects"] = "Clients has open projects";
$lang["clients_has_hold_projects"] = "Clients has hold projects";
$lang["clients_has_completed_projects"] = "Clients has completed projects";
$lang["clients_has_canceled_projects"] = "Clients has canceled projects";

$lang["has_open_estimates"] = "Has open estimates";
$lang["has_accepted_estimates"] = "Has accepted estimates";
$lang["has_new_estimate_requests"] = "Has new estimate requests";
$lang["has_estimate_requests_in_progress"] = "Has estimate requests in progress";
$lang["clients_has_open_estimates"] = "Client has open estimates";
$lang["clients_has_accepted_estimates"] = "Clients has accepted estimates";
$lang["clients_has_new_estimate_requests"] = "Clients has new estimate requests";
$lang["clients_has_estimate_requests_in_progress"] = "Clients has estimate requests in progress";

$lang["has_open_tickets"] = "Has open tickets";
$lang["clients_has_open_tickets"] = "Clients has open tickets";

$lang["has_new_orders"] = "Has new orders";
$lang["clients_has_new_orders"] = "Clients has new orders";

$lang["has_open_proposals"] = "Has open proposals";
$lang["has_accepted_proposals"] = "Has accepted proposals";
$lang["has_rejected_proposals"] = "Has rejected proposals";
$lang["clients_has_open_proposals"] = "Clients has open proposals";
$lang["clients_has_accepted_proposals"] = "Clients has accepted proposals";
$lang["clients_has_rejected_proposals"] = "Clients has rejected proposals";

$lang["logged_in_today"] = "Logged in today";
$lang["logged_in_last_seven_days"] = "Logged in last 7 days";

$lang["hide_from_kanban_view"] = "Hide from kanban view";

/* Version 2.9 */

$lang["contract"] = "Contract";
$lang["contracts"] = "Contracts";
$lang["can_access_contracts"] = "Can access contracts?";
$lang["show_in_contract"] = "Show in contract";
$lang["contract_date"] = "Contract date";
$lang["edit_contract"] = "Edit contract";
$lang["delete_contract"] = "Delete contract";
$lang["contract_sent_message"] = "The contract has been sent!";
$lang["add_contract"] = "Add contract";
$lang["contract_preview"] = "Contract preview";
$lang["clone_contract"] = "Clone contract";
$lang["contract_to"] = "Contract to";
$lang["contract_settings"] = "Contract settings";
$lang["contract_color"] = "Contract color";
$lang["send_contract_bcc_to"] = "When sending contract to client, send BCC to";
$lang["initial_number_of_the_contract"] = "Initial number of the contract";
$lang["the_contracts_id_must_be_larger_then_last_contract_id"] = "The contracts ID must be larger than the last contract ID.";
$lang["contract_sent"] = "Contract sent";
$lang["notification_contract_sent"] = "Sent a contract";
$lang["contract_accepted"] = "Contract accepted";
$lang["notification_contract_accepted"] = "Accepted a contract";
$lang["contract_rejected"] = "Contract rejected";
$lang["notification_contract_rejected"] = "Rejected a contract";
$lang["create_estimate"] = "Create Estimate";
$lang["include_all_items_of_this_contract"] = "Include all items of this contract";
$lang["contract_view"] = "Contract view";
$lang["accept_contract"] = "Accept contract";
$lang["reject_contract"] = "Reject contract";
$lang["contract_accepted_message"] = "You've successfully accepted this contract!";

$lang["contract_editor"] = "Contract Editor";
$lang["contract_templates"] = "Contract templates";
$lang["add_contract_template"] = "Add contract template";
$lang["edit_contract_template"] = "Edit contract template";
$lang["delete_contract_template"] = "Delete contract template";
$lang["use_template_from"] = "Use template from";
$lang["print_contract"] = "Print contract";
$lang["contract_template_inserting_instruction"] = "You'll lost all unsaved changes by inserting a template.";

$lang["ticket_info"] = "Ticket info";

$lang["recurring_tasks"] = "Recurring tasks";
$lang["add_multiple_contacts"] = "Add multiple contacts";

$lang["total_invoiced"] = "Total invoiced";

$lang["show_sub_tasks"] = "Show sub tasks";

$lang["add_signature_option_on_accepting_estimate"] = "Add signature option on accepting estimate";
$lang["accept_estimate"] = "Accept estimate";

$lang["sub_tasks_completed"] = "Sub tasks completed";

$lang["client_portal"] = "Client portal";
$lang["sales_and_prospects"] = "Sales & Prospects";

$lang["contract_prefix"] = "Contract prefix";
$lang["default_contract_template"] = "Default contract template";

$lang["default_proposal_template"] = "Default proposal template";

$lang["signed_date"] = "Signed date";

$lang["add_signature_option_on_accepting_contract"] = "Add signature option on accepting contract";
$lang["accept"] = "Accept";
$lang["signer_info"] = "Signer info";
$lang["default_template"] = "Default template";
$lang["change_template"] = "Change template";

/* Version 2.9.2 */

$lang["custom_left_menu_instruction"] = "For external links, please add http or https before the url.";
$lang["parent_task_completing_error_message"] = "There has incompleted sub task(s) of this task!";

/* Version 3.0 */

$lang["was_this_article_helpful"] = "Was this article helpful?";
$lang["thank_you_for_your_feedback"] = "Thank you for your feedback.";
$lang["feedback"] = "Feedback";

$lang["add_signature_option_for_team_members"] = "Add signature option for team members";
$lang["sign_contract"] = "Sign contract";

$lang["remove_task_statuses"] = "Remove task statuses";
$lang["task_statuses"] = "Task Statuses";

$lang["file_delete_permission_error_message"] = "We can't delete some files since you don't have permission.";

$lang["reject_estimate"] = "Reject estimate";

$lang["unknown_user"] = "Unknown user";

$lang["yes_specific_client_groups"] = "Yes, specific client groups";

/* Version 3.1 */

$lang["add_company"] = "Add company";
$lang["edit_company"] = "Edit company";
$lang["delete_company"] = "Delete company";
$lang["default_company"] = "Default company";

$lang["task_priority"] = "Task priority";
$lang["priority"] = "Priority";
$lang["add_task_priority"] = "Add task priority";
$lang["edit_task_priority"] = "Edit task priority";
$lang["delete_task_priority"] = "Delete task priority";

$lang["import_items"] = "Import items";
$lang["import_error_field_required"] = "%s field is required";

$lang["do_not_show_projects"] = "Don't show projects";

$lang["show_in_kanban"] = "Show in kanban";
$lang["project_name"] = "Project name";
$lang["client_name"] = "Client name";

$lang["import_date_error_message"] = "Date format is invalid.";

$lang["event_label"] = "Event label";

$lang["undo"] = "Undo";

$lang["clone_expense"] = "Clone expense";
$lang["files_will_not_be_copied"] = "Files will not be copied.";

$lang["checklist_group"] = "Checklist group";
$lang["checklists"] = "Checklists";
$lang["add_checklist_group"] = "Add checklist group";
$lang["edit_checklist_group"] = "Edit checklist group";
$lang["delete_checklist_group"] = "Delete checklist group";
$lang["select_from_checklist_group"] = "Select from checklist group";

$lang["import_leaves"] = "Import leaves";

$lang["import_tasks"] = "Import tasks";

$lang["import_not_exists_error_message"] = "%s not found.";
$lang["import_task_points_error_message"] = "Points field is invalid.";
$lang["user"] = "User";

$lang["checkout"] = "Checkout";
$lang["all_plugins"] = "All plugins";

$lang["payments_summary"] = "Payments Summary";
$lang["yearly_summary"] = "Yearly summary";
$lang["clients_summary"] = "Clients summary";

$lang["import_leave_status_error_message"] = "Status is invalid. Valid statuses are: ";

$lang["import_expense"] = "Import expense";

$lang["mark_as_default"] = "Mark as default";
$lang["remove_as_default"] = "Remove as default";
$lang["staff_default_dashboard_help_message"] = "This will replace the default dashboard for all team members. Please note that, the widgets will be distributed as per user's permission.";

/* Version 3.2 */

$lang["reminder"] = "Reminder";
$lang["reminders"] = "Reminders";
$lang["show_all_reminders"] = "Show all reminders";
$lang["time"] = "Time";
$lang["add_reminder"] = "Add reminder";
$lang["delete_reminder"] = "Delete reminder";
$lang["snooze"] = "Snooze";
$lang["dismiss"] = "Dismiss";
$lang["snooze_length"] = "Snooze length";
$lang["minutes"] = "Minutes";
$lang["reminder_sound_volume"] = "Reminder sound volume";
$lang["reminder_details"] = "Reminder details";
$lang["mark_as_done"] = "Mark as done";
$lang["client_can_create_reminders"] = "Client can create reminders?";

$lang["php_file_format_is_not_allowed"] = "PHP file format is not allowed!";

$lang["projects_overview"] = "Projects Overview";
$lang["progression"] = "Progression";

$lang["this_year"] = "This Year";
$lang["last_year"] = "Last Year";
$lang["last_12_months"] = "Last 12 months";

$lang["estimate_sent_statistics"] = "Estimate sent statistics";

$lang["title_language_key"] = "Title Language Key";
$lang["placeholder_language_key"] = "Placeholder Language Key";
$lang["keep_it_blank_if_you_do_not_use_translation"] = "Keep it blank if you don't use translation";
$lang["language_key_recommendation_help_text"] = "Recommended to use any prefix like custom_field_";

$lang["other"] = "Other";

$lang["print_estimate"] = "Print estimate";

$lang["the_person_who_will_manage_this_client"] = "The person who'll manage this client.";
$lang["the_person_who_will_manage_this_lead"] = "The person who'll manage this lead.";

$lang["language_key"] = "Language Key";
$lang["left_menu_language_key_recommendation_help_text"] = "Recommended to use any prefix like left_menu_";

$lang["project_type"] = "Project type";
$lang["client_project"] = "Client Project";
$lang["internal_project"] = "Internal Project";

$lang["contact_info"] = "Contact info";
$lang["type"] = "Type";
$lang["organization"] = "Organization";
$lang["person"] = "Person";

$lang["last_announcement"] = "Last announcement";
$lang["no_announcement_yet"] = "No announcement yet!";

$lang["team_members_overview"] = "Team Members Overview";
$lang["on_leave_today"] = "On leave today";

$lang["enable_embedded_form_to_get_leads"] = "Enable embedded form to get leads";
$lang["please_submit_the_form"] = "Please submit the form";
$lang["show_in_embedded_form"] = "Show in embedded form";

$lang["after_submit"] = "After submit";
$lang["return_json_response"] = "Return json response";
$lang["show_text_result"] = "Show text result";
$lang["redirect_to_this_url"] = "Redirect to this url:";

$lang["yes_only_own_timelogs"] = "Yes, only own timelogs";
$lang["yes_only_own_project_members"] = "Yes, only own project members";
$lang["excluding_his_her_timelogs"] = "Excluding his/her own timelogs";
$lang["can_add_own_timelogs_only"] = "Can add own timelogs only";

$lang["all_tasks_overview"] = "All Tasks Overview";

$lang["invoice_overview"] = "Invoice Overview";

$lang["next_reminder"] = "Next reminder";

$lang["new_tickets_in_last_30_days"] = "New tickets in last 30 days";

$lang["individual"] = "Individual";

$lang["total_after_discount"] = "Total After Discount";

/* Version 3.3 */

$lang["change_the_tasks_start_date_and_deadline_based_on_project_start_date"] = "Change the tasks start date and deadline based on project start date";

$lang["can_edit_only_own_created_projects"] = "Can edit only own created projects";
$lang["can_delete_only_own_created_projects"] = "Can delete only own created projects";

$lang["checklist_status"] = "Checklist status";

/* Version 3.4 */

$lang["subscribe"] = "Subscribe";
$lang["email_protocol"] = "Email protocol";

$lang["please_enable_the_file_uploads_php_settings"] = "Please enable the file_uploads php setting in the server.";
$lang["file_size_too_large"] = "File size is too large. Please increase the upload_max_filesize from server.";

$lang["sub_task_status"] = "Sub task status";

$lang["can_access_client_feedback_in_projects"] = "Can access client feedback in projects";
$lang["change_the_milestone_dates_based_on_project_start_date"] = "Change the milestone dates based on project start date";

$lang["send_first_due_invoice_reminder_notification_before"] = "Send 1st due invoice reminder before due date";
$lang["send_second_due_invoice_reminder_notification_before"] = "Send 2nd due invoice reminder before due date";

$lang["send_first_invoice_overdue_reminder_after"] = "Send 1st invoice overdue reminder after";
$lang["send_second_invoice_overdue_reminder_after"] = "Send 2nd invoice overdue reminder after";

$lang["product"] = "Product";

$lang["subscription_id"] = "Subscription ID";
$lang["subscription_sent_message"] = "The subscription has been sent!";
$lang["add_subscription"] = "Add subscription";
$lang["edit_subscription"] = "Edit subscription";
$lang["delete_subscription"] = "Delete subscription";
$lang["subscription"] = "Subscription";
$lang["subscriptions"] = "Subscriptions";
$lang["subscription_value"] = "Subscription Value";
$lang["subscription_items"] = "Subscription items";
$lang["email_subscription_to_client"] = "Email subscription to client";
$lang["send_subscription"] = "Send subscription";
$lang["subscription_settings"] = "Subscription Settings";
$lang["subscription_prefix"] = "Subscription prefix";
$lang["initial_number_of_the_subscription"] = "Initial number of the subscription";
$lang["can_access_subscriptions"] = "Can access subscriptions?";
$lang["show_in_subscription"] = "Show in subscription";
$lang["subscription_total"] = "Subscription total";
$lang["start_subscription"] = "Start subscription";

$lang["subscription_success_message"] = "The subscription has been started successfully.";

$lang["enable_stripe_subscription"] = "Enable Stripe subscription";
$lang["please_enable_the_stripe_payment_method_first"] = "Please enable the stripe payment method first!";
$lang["tax_mapping"] = "Tax mapping";
$lang["mapped"] = "Mapped";
$lang["select_stripe_tax"] = "Select Stripe tax";
$lang["stripe_price_error_message"] = "App subscription price and recurring interval should be same as stripe product price and interval.";
$lang["stripe_tax_error_message"] = "Some taxes are not mapped with stripe yet. Please map the taxes from the subscription settings.";
$lang["payment_status"] = "Payment status";
$lang["failed"] = "Failed";
$lang["next_billing_date"] = "Next billing date";
$lang["cancel_subscription"] = "Cancel subscription";

$lang["invoice_number"] = "Invoice number";
$lang["estimate_number"] = "Estimate number";
$lang["order_number"] = "Order number";

$lang["client_can_access_notes"] = "Client can access notes?";

$lang["my_tasks_overview"] = "My Tasks Overview";

$lang["leads_overview"] = "Leads Overview";
$lang["converted_to_client"] = "Converted to client";

$lang["remember_to_add_this_urls_in_authorized_redirect_uri"] = "Remember to add this urls in Authorized redirect uri";

$lang["merge"] = "Merge";
$lang["move_all_comments_or_notes_from"] = "Move all comments/notes from";
$lang["moved_to"] = "Moved to";

$lang["ok"] = "OK";
$lang["app"] = "App";
$lang["stripe"] = "Stripe";
$lang["activate_as_stripe_subscription"] = "Activate as Stripe subscription";
$lang["activate_as_internal_subscription"] = "Activate as internal subscription";
$lang["activate_as_stripe_subscription_message_1"] = "Please map the stripe product and price with this subscription. You can add the stripe products and prices from your stripe dashboard.";
$lang["activate_as_stripe_subscription_message_2"] = "The client will get a request to add the payment method with this subscription. Once the client submit the payment method, this subscription will be activated and auto payment will be enabled via stripe.";
$lang["activate_as_internal_subscription_message_1"] = "This subscription will be managed by app. The invoices will be created based on the subscription terms.";
$lang["activate_as_internal_subscription_message_2"] = "Note: Payments will not be done automatically. For automated payment, you can use the stripe subscription.";
$lang["subscription_toatl_can_not_empty_message"] = "Subscription total can't be 0.";

$lang["subscription_request_sent"] = "Subscription request sent";
$lang["notification_subscription_request_sent"] = "New subscription request";

$lang["first_billing_date"] = "First billing date";
$lang["first_billing_date_cant_be_past_message"] = "First billing date can’t be past. Keep it blank to use the date when the client will subscribe.";

$lang["gst_number"] = "GST Number";

$lang["announcement_created"] = "Announcement created";

$lang["company_logo"] = "Company Logo";

$lang["task_commented"] = "Task commented";
$lang["task_assigned"] = "Task assigned";
$lang["task_general"] = "Task general";

/* Version 3.5 */

$lang["visitors_can_see_store_before_login"] = "Visitors can see store before login";
$lang["show_payment_option_after_submitting_the_order"] = "Show payment option after submitting the order";
$lang["accept_order_before_login"] = "Accept order before login";
$lang["proceed_to_payment"] = "Proceed to payment";
$lang["pay_order"] = "Pay order";
$lang["order_status_after_payment"] = "Order status after payment";
$lang["store_settings"] = "Store settings";
$lang["banner_image_on_public_store"] = "Banner image on public store";
$lang["your_order_has_been_submitted"] = "Your order has been submitted.";

$lang["re_captcha_error-timeout-or-duplicate"] = "The reCAPTCHA has been expired or duplicate. Please reload the page.";

$lang["related_to"] = "Related to";

$lang["hide_from_non_project_related_tasks"] = "Hide from non-project related tasks";
$lang["add_task_in_project"] = "Add task in project";

$lang["general_task"] = "General task";
$lang["general_task_created"] = "General task created";
$lang["general_task_updated"] = "General task updated";
$lang["general_task_assigned"] = "General task assigned";
$lang["general_task_started"] = "General task started";
$lang["general_task_finished"] = "General task finished";
$lang["general_task_reopened"] = "General task reopened";
$lang["general_task_deleted"] = "General task deleted";
$lang["general_task_commented"] = "General task commented";

$lang["notification_general_task_created"] = "Created a new task.";
$lang["notification_general_task_updated"] = "Updated a task.";
$lang["notification_general_task_assigned"] = "Assigned a task to %s."; //Assigned a task to Mr. X
$lang["notification_general_task_started"] = "Started a task.";
$lang["notification_general_task_finished"] = "Finished a task.";
$lang["notification_general_task_reopened"] = "Reopened a task.";
$lang["notification_general_task_deleted"] = "Deleted a task.";
$lang["notification_general_task_commented"] = "Commented on a task.";

$lang["bookmark"] = "Bookmark";
$lang["bookmark_icon"] = "Bookmark Icon";
$lang["change_filters"] = "Change filters";
$lang["manage_filters"] = "Manage Filters";
$lang["new_filter"] = "New filter";
$lang["update_filter"] = "Update filter";
$lang["add_new_filter"] = "Add new filter";
$lang["show_time_with_task_start_date_and_deadline"] = "Show time with task start date and deadline";

$lang["save_and_continue_to_login_for_payment"] = "Save & continue to login for payment";
$lang["public_store_page_setting_help_message"] = "You can set the store as landing page by adding 'store' in the landing page setting.";
$lang["public_store_page_setting_permission_error_message"] = "Please enable the store access permission for clients.";
$lang["order_status_after_payment_help_message"] = "This will be applicable only when the order status is";

$lang["subscription_first_billing_date_error_message"] = "The first billing date should be before the next billing date based on the billing period!";
$lang["client_currency_not_editable_message"] = "The currency is not editable when there is any invoice, estimate, order, proposal, contract or subscription.";

$lang["subscription_started"] = "Subscription started";
$lang["notification_subscription_started"] = "Started a subscription.";

$lang["subscription_invoice_created_via_cron_job"] = "Subscription invoice created via Cron Job";
$lang["notification_subscription_invoice_created_via_cron_job"] = "New invoice generated from subscription.";

$lang["create_credit_note"] = "Create credit note";
$lang["create_credit_note_message"] = "Are you sure? You're going to fully credit this invoice. A credit note will be created for this invoice.";
$lang["credited"] = "Credited";
$lang["credit_note"] = "Credit Note";
$lang["email_credit_note_to_client"] = "Email credit note to client";
$lang["main_invoice"] = "Main invoice";
$lang["credit_note_id"] = "Credit note ID";
$lang["send_credit_note"] = "Send credit note";

$lang["taxable"] = "Taxable";

$lang["can_manage_team_members_notes"] = "Can manage team member's notes?";
$lang["team_members_can_not_see_own_notes"] = "Team members can't see own notes.";

$lang["articles_order"] = "Articles order";
$lang["top_menu"] = "Top menu";
$lang["edit_top_menu"] = "Edit top menu";

$lang["top_menu_description_message"] = "This menu will be visible only in the public pages";
$lang["enable_top_menu"] = "Enable top menu";
$lang["menu_items"] = "Menu items";

$lang["landing_page"] = "Landing page";
$lang["landing_page_help_text"] = "Normally keep it blank. Set this value only if you want any custom landing page. (Example value: knowledge_base)";

$lang["fixed_amount_discount_before_tax_error_message"] = "Fixed amount discount can't be added before tax.";

$lang["invoices_summary"] = "Invoices summary";
$lang["estimates_summary"] = "Estimates summary";
$lang["leads_summary"] = "Leads summary";
$lang["orders_summary"] = "Orders summary";
$lang["estimate_request_summary"] = "Estimate request summary";
$lang["proposals_summary"] = "Proposals summary";
$lang["expenses_summary"] = "Expenses summary";
$lang["monthly_summary"] = "Monthly summary";

$lang["yearly_chart"] = "Yearly chart";
$lang["category_chart"] = "Category chart";

$lang["count"] = "Count";
$lang["invoice_total"] = "Invoice total";
$lang["order_total"] = "Order total";

$lang["last_7_days"] = "Last 7 Days";
$lang["next_7_days"] = "Next 7 Days";
$lang["last_30_days"] = "Last 30 Days";
$lang["this_month"] = "This Month";
$lang["last_month"] = "Last Month";
$lang["next_month"] = "Next Month";
$lang["next_year"] = "Next Year";

$lang["hold_projects"] = "Hold Projects";
$lang["open_tasks"] = "Open Tasks";
$lang["completed_tasks"] = "Completed Tasks";

$lang["team_members_summary"] = "Team members summary";
$lang["created_date_wise"] = "Created date wise";
$lang["conversion_date_wise"] = "Conversion date wise";

$lang["ticket_statistics"] = "Ticket Statistics";

$lang["can_activate_deactivate_team_members"] = "Can activate deactivate team members?";
$lang["can_delete_team_members"] = "Can delete team members?";

$lang["project_settings"] = "Project settings";
$lang["project_status"] = "Project Status";
$lang["add_project_status"] = "Add project status";
$lang["mark_project_as"] = "Mark Project as";

$lang["status_language_key_recommendation_help_text"] = "Recommended to use any prefix like project_status_";
$lang["edit_project_status"] = "Edit project status";
$lang["delete_project_status"] = "Delete project status";

$lang["open_project_status_recommendation_help_text"] = "You can change the text, but this status will be considered as initial open status.";
$lang["completed_project_status_recommendation_help_text"] = "You can change the text, but this status will be considered as completed.";

$lang["full_width"] = "Full width";
$lang["hide_topbar"] = "Hide topbar";

/* Version 3.5.1 */

$lang["enable_lock_state"] = "Enable lock state";
$lang["invoice_lock_state_description"] = "Once you enable the lock state, the invoices can’t be edited after sending to client or changing the status.";
$lang["estimate_lock_state_description"] = "Once you enable the lock state, the accepted estimates can’t be edited.";
$lang["proposal_lock_state_description"] = "Once you enable the lock state, the accepted proposals can’t be edited.";
$lang["contract_lock_state_description"] = "Once you enable the lock state, the accepted contracts can't be edited.";

/* Version 3.6 */

$lang["file_manager"] = "File manager";

$lang["all_files"] = "All files";
$lang["recent_uploads"] = "Recent uploads";

$lang["favorites"] = "Favorites";
$lang["new_folder"] = "New folder";
$lang["folder_details"] = "Folder details";
$lang["file_details"] = "File details";
$lang["manage_access"] = "Manage access";
$lang["root_folder"] = "Home";
$lang["authorized_team_members"] = "Authorized team members";

$lang["full_access"] = "Full access";
$lang["full_access_placeholder"] = "Choose who can manage everything";
$lang["upload_and_organize"] = "Upload & Organize";
$lang["upload_only"] = "Upload only";

$lang["folder_permission_instruction"] = "This permission will be applicable to this folder and all of its subfolders.";
$lang["all_clients"] = "All Clients";

$lang["select_a_file_to_view_details"] = "Select a file of folder to view its details";
$lang["empty"] = "Empty";
$lang["folder"] = "Folder";
$lang["folders"] = "Folders";
$lang["rename"] = "Rename";
$lang["rename_folder"] = "Rename folder";

$lang["folder_delete_confirmation_message"] = "Are you sure you want to permanently delete this folder and all of the subfolders and files?";
$lang["file_delete_confirmation_message"] = "Are you sure you want to permanently delete this file?";
$lang["explore"] = "Explore";
$lang["add_to_favorites"] = "Add to Favorites";
$lang["remove_from_favorites"] = "Remove from Favorites";
$lang["uploaded_at"] = "Uploaded at";
$lang["created_at"] = "Created at";
$lang["who_has_access"] = "How has access";

$lang["search_folder_or_file"] = "Search folder or file";
$lang["move"] = "Move";
$lang["move_folder"] = "Move folder";
$lang["move_file"] = "Move file";

$lang["enable_audio_recording"] = "Enable audio recording";
$lang["add_webm_file_format_to_enable_audio_recording"] = "Add webm file formate to enable audio recording.";

$lang["reference"] = "Reference";
$lang["link_copied"] = "Link copied!";
$lang["recording"] = "Recording...";

$lang["https_required"] = "HTTPS required.";

$lang["info"] = "Info";
$lang["select_any_folder_for_move"] = "Select any folder for move.";

$lang["enable_background_image_for_pdf"] = "Enable background image for pdf";
$lang["pdf_background_image"] = "Pdf background image (510x720)";
$lang["set_background_only_on_first_page"] = "Set background only on 1st page";
$lang["invoice_item_list_background_color"] = "Invoice item list background color";

$lang["logo"] = "Logo";
$lang["company_info"] = "Company Info";
$lang["change_invoice_logo"] = "Change invoice logo";

$lang["year"] = "Year";
$lang["invoice_number_format"] = "Invoice number format";
$lang["auto_increment_digits"] = "Auto increment %s Digits";
$lang["year_based_on"] = "Year based on";
$lang["reset_invoice_number_every_year"] = "Reset invoice number every year";

$lang["style"] = "Style";

$lang["attach_pdf"] = "Attach PDF";
$lang["attachment_size_is_too_large"] = "Attachment size is too large. It can't be attached with this email.";

$lang["files_list"] = "Files list";

$lang["hide_fields_on_lead_embedded_form"] = "Hide fields from lead embedded forms";
$lang["unspecified"] = "Unspecified";

$lang["import_team_members"] = "Import team members";
$lang["import_team_member_error_name_field_required"] = "First name and last name is both required to add a team member.";
$lang["import_team_member_error_job_title_field_required"] = "Job title field is required.";
$lang["import_team_member_error_email_field_required"] = "Email field is required.";

$lang["only_admin_users_can_set_the_admin_role"] = "Only admin users can set the Admin role.";

$lang["import_projects"] = "Import projects";
$lang["import_project_error_title_field_required"] = "Project title field is required.";
$lang["import_project_error_project_type_field_required"] = "Project type field is required.";
$lang["import_project_error_client_field_required"] = "Since it's client project, client field is required.";
$lang["import_project_error_client_name"] = "Client name is invalid.";
$lang["import_project_error_project_status"] = "Project status is invalid.";

$lang["import_contacts"] = "Import contacts";
$lang["import_contact_error_name_field_required"] = "First name and last name are both required to add a contact.";
$lang["import_contact__error_client_field_required"] = "Client field is required.";
$lang["import_contact_error_client_name"] = "Client name is invalid.";
$lang["import_gender_is_invalid"] = "Gender is invalid.";

$lang["import_error_name_field_required"] = "Name field is required.";
$lang["import_error_type_field_required"] = "Type field is required.";
$lang["import_error_invalid_type"] = "Invalid type.";

$lang["can_access_everything"] = "Can access everything";
$lang["user_permissions"] = "User permissions";
$lang["can_access_only"] = "Can access only";

$lang["email_seen_at"] = "Email seen at";
$lang["email_seen_count"] = "Email seen count";

$lang["last_preview_seen"] = "Last preview seen";
$lang["last_email_seen"] = "Last email seen";

$lang["subscription_cancelled"] = "Subscription cancelled";
$lang["notification_subscription_cancelled"] = "Cancelled a subscription";

$lang["enable_comments_on_proposals"] = "Enable comments on proposals";
$lang["show_most_recent_proposal_comments_at_the_top"] = "Show most recent proposal comments at the top";
$lang["proposal_commented"] = "Proposal commented";
$lang["proposal_creator"] = "Proposal creator";
$lang["notification_proposal_commented"] = "Commented on a proposal.";

$lang["can_upload_and_edit_files"] = "Can upload and edit files";
$lang["can_comment_on_projects"] = "Can comment on projects";
$lang["can_view_files"] = "Can view files";

$lang["default_permissions_for_non_primary_contact"] = "Default permissions for non-primary contact";
$lang["primary_contact_will_get_full_permission_message"] = "Note: The primary contact will get full permission.";
$lang["permission_is_required"] = "Permission is required.";
$lang["make_primary_contact_help_message"] = "The existing primary contact will still have full access. You can change the permission anytime.";
$lang["primary_contact_can_manage_the_permission_of_other_contacts"] = "Primary contact can manage the permission of other contacts.";

/* Version 3.7 */

$lang["dynamic"] = "Dynamic";
$lang["select_range"] = "Select range";

$lang["proposal_preview_opened"] = "Proposal preview opened";
$lang["notification_proposal_preview_opened"] = "Opened a proposal preview.";
$lang["proposal_email_opened"] = "Proposal email opened";
$lang["notification_proposal_email_opened"] = "Opened a proposal email.";

$lang["create_contract"] = "Create Contract";

$lang["create_as_a_non_subtask"] = "Create as a non-subtask";
$lang["install_this_app"] = "Install this app.";
$lang["app_color"] = "App color";

$lang["skip"] = "Skip";

$lang["self_improvements"] = "Self Improvements";
$lang["business_growth"] = "Business Growth";
$lang["sales_management"] = "Sales Management";
$lang["customer_support"] = "Customer Support";
$lang["team_management"] = "Team Management";
$lang["collaboration"] = "Collaboration";

$lang["send_first_reminder_before"] = "Send 1st reminder before";
$lang["send_second_reminder_before"] = "Send 2nd reminder before";
$lang["subscription_renewal_reminder"] = "Subscription renewal reminder";
$lang["notification_subscription_renewal_reminder"] = "Subscription renewal reminder";

$lang["enable_tinymce"] = "Enable TinyMCE";
$lang["tinymce_api_key"] = "TinyMCE API key";

$lang["protocol"] = "Protocol";
$lang["re_captcha_suspicious_activity"] = "The reCAPTCHA detected suspicious activity.";

$lang["all_contacts_of_the_client"] = "All contacts of the client";
$lang["specific_contacts_of_the_client"] = "Specific contacts of the client";

$lang["please_do_not_use_duplicate_variables"] = "Please don't use duplicate variables.";
$lang["please_do_not_use_invalid_special_character"] = "Please don't use invalid special character.";
$lang["please_use_any_serial"] = "Please use any serial.";

$lang["add_automation"] = "Add automation";
$lang["edit_automation"] = "Edit automation";

$lang["automations"] = "Automations";

$lang["match_any"] = "Match any";
$lang["match_all"] = "Match all";
$lang["email_address"] = "Email address";
$lang["email_subject"] = "Email subject";
$lang["email_content"] = "Email content";

$lang["imap_email_received"] = "IMAP email received";
$lang["new_ticket_created_by_imap_email"] = "New ticket created by IMAP email";

$lang["conditions"] = "Conditions";
$lang["block_ticket_creation"] = "Block ticket creation";
$lang["if"] = "If";

$lang["small_letter_field"] = "______ field";
$lang["small_letter_condition_is_equal"] = "is equal to";
$lang["small_letter_condition_is_not_equal"] = "is not equal to";
$lang["small_letter_condition_is_in_list"] = "is in list";
$lang["small_letter_condition_is_not_in_list"] = "is not in list";

$lang["small_letter_condition_is_contains"] = "is contains";
$lang["small_letter_condition_is_not_contains"] = "is not contains";
$lang["small_letter_condition_is_contains_in_list"] = "is contains in list";
$lang["small_letter_condition_is_not_contains_in_list"] = "is not contains in list";
$lang["small_letter_something"] = "something";
$lang["small_letter_is_something"] = "is something";
$lang["small_letter_and"] = "and";
$lang["small_letter_or"] = "or";

$lang["select_placeholder"] = "Select...";
$lang["select_placeholder_type_and_press_enter"] = "Type and press Enter";

$lang["do_something"] = "Do something...";
$lang["do_not_create_ticket"] = "Do not create ticket";
$lang["set_field_"] = "Set";
$lang["please_input_all_required_fields"] = "Please input all required fields.";
$lang["grid"] = "Grid";

$lang["disable_pdf_for_clients"] = "Disable PDF for clients";

$lang["select_specific"] = "Select specific";
$lang["clear_selection"] = "Clear selection";
$lang["download_selected_items"] = "Download selected items";
$lang["delete_selected_items"] = "Delete selected items";
$lang["year_or_month_based_on"] = "Year/month based on";

$lang["none"] = "None";
$lang["trigger_manually"] = "Trigger Manually";

/* Version 3.8 */

$lang["field_type_multiple_choice"] = "Multiple Choice";
$lang["field_type_checkboxes"] = "Checkboxes";

$lang["insert_into_editor"] = "Insert into editor";
$lang["just_now"] = "Just now";
$lang["minute_ago"] = "minute ago";
$lang["minutes_ago"] = "minutes ago";
$lang["hour_ago"] = "hour ago";
$lang["hours_ago"] = "hours ago";
$lang["day_ago"] = "day ago";
$lang["days_ago"] = "days ago";
$lang["month_ago"] = "month ago";
$lang["months_ago"] = "months ago";
$lang["year_ago"] = "year ago";
$lang["years_ago"] = "years ago";

$lang["support_info"] = "Support info";
$lang["in_messages"] = "In messages";
$lang["out_messages"] = "Out messages";

$lang["ticket_tasks"] = "Ticket tasks";
$lang["has_number_of_tickets_tickets"] = "Has %s tickets";

$lang["signup_and_login"] = "Signup & Login";
$lang["ui"] = "UI";
$lang["projects_and_tasks"] = "Projects & Tasks";
$lang["others"] = "Others";

$lang["show_the_status_checkbox_in_tasks_list"] = "Show the status checkbox in tasks list";

$lang["pwa_install_message_for_iphone"] = "Click on %s icon, 'Add to Home Screen' from the browser menu and add this app to your home screen for easy access.";
$lang["got_it"] = "Got it!";

$lang["empty_comment_cannot_be_saved"] = "Empty comment cannot be saved.";

$lang["short_ticket_templates"] = "Templates";

/* Version 3.8.2 */

$lang["sortable"] = "Sortable";
$lang["total_hours"] = "Total hours";
$lang["support_only_project_related_tasks_globally_label"] = "Create only the project related tasks from the global task creation modal";

$lang["name_and_company_name_error_message"] = "First name, last name, and company name can't be hidden at a time.";

/* Version 3.9 */

$lang["e_invoice"] = "e-Invoice";
$lang["enable_e_invoice"] = "Enable e-Invoice";
$lang["e_invoice_templates"] = "e-Invoice Templates";
$lang["e_invoice_template"] = "e-Invoice template";
$lang["e_invoice_template_title"] = "e-Invoice template title";
$lang["edit_e_invoice_template"] = "Edit e-Invoice template";
$lang["delete_e_invoice_template"] = "Delete e-Invoice template";
$lang["default_e_invoice_template"] = "Default e-Invoice template";
$lang["default_e_invoice_template_for_credit_note"] = "Default e-Invoice template for credit note";

$lang["company_or_supplier_or_seller"] = "Company (Supplier/Seller)";
$lang["client_or_customer_or_buyer"] = "Client (Customer/Buyer)";
$lang["template_variable_name"] = "Template variable name";

$lang["e_invoice_template_custom_field_variable_info"] = "If you are looking for more variables, please add them in the related custom field.";
$lang["send_e_invoice_attachment_with_email"] = "Send e-Invoice attachment with email";

$lang["validate_and_save"] = "Validate & Save";
$lang["copied"] = "Copied!";
$lang["undefined_variables"] = "Undefined variables";

$lang["related_articles"] = "Related articles";
$lang["show_related_articles_by_labels"] = "Show related articles by labels";
$lang["banner_image"] = "Banner image";
$lang["banner_url"] = "Banner URL";

$lang["invoice_info"] = "Invoice info";
$lang["you_can_validate_the_xml_file_before_sending_it"] = "You can validate the XML file before sending it.";
$lang["unlink_xml_attachment"] = "Unlink XML attachment";
$lang["unattached"] = "Unattached";

$lang["enable_invoice_id_editing"] = "Enable invoice ID editing";

$lang["yes_all_expenses"] = "Yes, all expenses";
$lang["can_manage_only_own_created_expenses"] = "Can manage only own created expenses";

$lang["set_invoice_permissions"] = "Set invoice permissions";
$lang["can_not_access_invoices"] = "Can’t access invoices";
$lang["can_manage_all_invoices"] = "Can manage all invoices";
$lang["can_view_all_invoices"] = "Can view all invoices";
$lang["can_manage_own_client_invoices"] = "Can manage own client’s invoices";
$lang["can_manage_own_client_invoices_except_delete"] = "Can manage own client’s invoices (Except delete)";
$lang["can_view_own_client_invoices"] = "Can view own client’s invoices";
$lang["can_manage_only_own_created_invoices"] = "Can manage only own created invoices";
$lang["can_manage_only_own_created_invoices_except_delete"] = "Can manage only own created invoices (Except delete)";

$lang["companies"] = "Companies";
$lang["panel"] = "Panel";

$lang["online_payment_disabled"] = "Online payment is disabled.";
$lang["contact_info"] = "Contact Info";
$lang["not_found"] = "Not found.";
$lang["last_online"] = "Last online";

$lang["proposal_info"] = "Proposal info";
$lang["proposal_items"] = "Proposal items";

$lang["enable_client_wallet"] = "Enable client wallet";
$lang["auto_balance_invoice_payments"] = "Auto balance invoice payments";

$lang["client_wallet"] = "Client wallet";
$lang["add_payment"] = "Add payment";
$lang["edit_payment"] = "Edit payment";

$lang["insufficient_balance_in_client_wallet"] = "Insufficient balance in client wallet.";
$lang["received_payments"] = "Received payments";
$lang["allocated_payments"] = "Allocated payments";
$lang["balance"] = "Balance";
$lang["wallet"] = "Wallet";
$lang["added_by"] = "Added by";
$lang["account_statement"] = "Account Statement";
$lang["period"] = "Period";
$lang["statement"] = "Statement";
$lang["opening_balance"] = "Opening balance";
$lang["generate_reports_based_on"] = "Generate reports based on";
$lang["all_time"] = "All time";
$lang["hide_team_members_list_from_dropdowns"] = "Hide team members list from dropdowns?";

/* Version 3.9.1 */

$lang["estimate_info"] = "Estimate info";
$lang["order_info"] = "Order info";
$lang["subscription_info"] = "Subscription info";
$lang["contract_info"] = "Contract info";

$lang["unassigned"] = "Unassigned";
$lang["enter_minimum_2_characters"] = "Please enter at least 2 characters.";

/* Version 3.9.3 */

$lang["add_todo"] = "Add to do";
$lang["managers"] = "Managers";

$lang["specific_members"] = "Specific members";
$lang["choose_members"] = "Choose members";
$lang["yes_all_proposals"] = "Yes, all proposals";
$lang["yes_only_own_proposals"] = "Yes, only own proposals";

$lang["invoice_details"] = "Invoice details";
$lang["client_vat_number"] = "Client VAT Number";

$lang["move_to_tab"] = "Move to Tab";
$lang["move_to_overview"] = "Move to Overview";

$lang["show_push_notification_even_the_window_is_not_in_focus"] = "Show push notification even the window is not in focus. (PWA only)";
$lang["pusher_beams_instance_id"] = "Pusher Beams Instance ID";
$lang["pusher_beams_primary_key"] = "Pusher Beams Primary Key";

/* Version 3.9.4 */

$lang["reset_filter"] = "Reset filter";
$lang["access_denied"] = "Access denied!";

$lang["can_not_access_contracts"] = "Can’t access contracts";
$lang["can_manage_all_contracts"] = "Can manage all contracts";
$lang["can_manage_only_own_client_contracts"] = "Can manage only own client’s contracts";
$lang["can_see_only_own_client_contracts"] = "Can see only own client’s contracts";

$lang["this_action_will_do_the_following"] = "This action will do the following:";
$lang["create_new_client"] = "Create a new client";
$lang["create_client_contact"] = "Create client contact";
$lang["add_client_contact"] = "Add client contact";
$lang["link_the_client_with_this_ticket"] = "Link the client with this ticket";
$lang["link_client_to_existing_client_suggestion"] = "A client with this email is already exists. Instead of creating a new client, you can link the client with this ticket.";

$lang["client_vat_or_gst_number"] = "VAT/GST Number";

$lang["ui_options"] = "UI Options";
$lang["filter_bar"] = "Filter Bar";
$lang["keep_filter_bar_collapsed"] = "Keep filter bar collapsed";
$lang["keep_filter_bar_expanded_until_any_saved_filter_is_selected"] = "Keep filter bar expanded until any saved filter is selected";
$lang["keep_filter_bar_always_expanded"] = "Keep filter bar always expanded";

return $lang;
